/**
 * UIManager - Manages UI components and status indicators
 * 
 * Handles creation, updates, and management of all UI elements
 * including status panels, tooltips, and visual feedback.
 * 
 * @version 2.0.0
 */

class UIManager {
    constructor() {
        this.components = new Map();
        this.isInitialized = false;
        this.updateQueue = [];
        this.isUpdating = false;
    }

    /**
     * Initialize the UI manager
     * @param {Object} client - Reference to the main MCP client
     * @param {StateManager} stateManager - State manager instance
     * @param {EventManager} eventManager - Event manager instance
     */
    initialize(client, stateManager, eventManager) {
        this.client = client;
        this.stateManager = stateManager;
        this.eventManager = eventManager;
        
        // Subscribe to state changes
        this.stateManager.subscribe('isConnected', (isConnected) => {
            this.updateConnectionStatus(isConnected);
        });
        
        this.stateManager.subscribe('mcpServers', (servers) => {
            this.updateToolsDisplay(servers);
        });
        
        this.stateManager.subscribe('settings.showStatusPanel', (show) => {
            this.updateStatusPanelVisibility(show);
        });
        
        this.stateManager.subscribe('settings.panelPosition', (position) => {
            this.updateStatusPanelPosition(position);
        });
        
        this.isInitialized = true;
        console.log('[UIManager] Initialized successfully');
    }

    /**
     * Create and add status indicators to the page
     * @async
     * @returns {Promise<void>}
     */
    async createStatusIndicators() {
        try {
            await this.createMcpToolsStatus();
            this.updateStatusPanelPosition();
            this.updateConnectionStatus();
            this.updateToolsDisplay();
        } catch (error) {
            console.error('[UIManager] Failed to create status indicators:', error);
            throw error;
        }
    }

    /**
     * Create the main MCP tools status panel
     * @private
     * @async
     * @returns {Promise<void>}
     */
    async createMcpToolsStatus() {
        // Remove existing panel if it exists
        const existingPanel = document.getElementById('mcp-tools-status');
        if (existingPanel) {
            existingPanel.remove();
        }

        const statusElement = document.createElement('div');
        statusElement.id = 'mcp-tools-status';
        statusElement.className = 'mcp-tools-status';
        statusElement.innerHTML = this.getStatusPanelHTML();

        // Find the best container for the panel
        const container = this.findBestContainer();
        container.appendChild(statusElement);

        // Store component reference
        this.components.set('statusPanel', statusElement);

        // Set up event listeners for the panel
        this.setupStatusPanelEvents(statusElement);

        console.log('[UIManager] MCP tools status panel created');
    }

    /**
     * Get HTML for status panel
     * @private
     * @returns {string} HTML string
     */
    getStatusPanelHTML() {
        return `
            <span class="mcp-label" style="padding-bottom: 9px;">Perplexity Web MCP Bridge</span>
            <div class="mcp-tools-header">
                <span class="status-indicator connecting" id="mcp-connection-dot"></span>
                <span class="status-text connecting" id="mcp-connection-text">Connecting...</span>
                <span class="tools-count-badge" id="mcp-tools-count-badge">0 MCP tools available</span>
            </div>
            <div class="mcp-tools-tooltip" id="mcp-tools-tooltip">
                <div class="tooltip-header">Available MCP Tools</div>
                <div class="tooltip-tools-list" id="mcp-tooltip-tools-list">
                    <div class="loading">Loading tools...</div>
                </div>
            </div>
        `;
    }

    /**
     * Find the best container for UI elements
     * @private
     * @returns {Element} Container element
     */
    findBestContainer() {
        // Try to find the main container
        const containerElement = document.querySelector('.\\@container\\/main');
        if (containerElement) {
            return containerElement;
        }

        // Fallback to body
        return document.body;
    }

    /**
     * Set up event listeners for status panel
     * @private
     * @param {Element} statusElement - Status panel element
     */
    setupStatusPanelEvents(statusElement) {
        // Set up tooltip event listeners
        this.setupTooltipEventListeners();
        
        // Add click handler for panel
        const clickHandler = this.eventManager.addEventListener(
            statusElement,
            'click',
            (e) => this.handleStatusPanelClick(e)
        );
        
        this.components.set('statusPanelClickHandler', clickHandler);
    }

    /**
     * Set up tooltip event listeners with event delegation
     * @private
     */
    setupTooltipEventListeners() {
        const toolsList = document.getElementById('mcp-tooltip-tools-list');
        if (!toolsList) return;

        // Remove existing listener to avoid duplicates
        const existingHandler = this.components.get('tooltipClickHandler');
        if (existingHandler) {
            this.eventManager.removeEventListener(existingHandler);
        }

        // Add event delegation for tool clicks
        const clickHandler = this.eventManager.addEventListener(
            toolsList,
            'click',
            (e) => this.handleTooltipClick(e)
        );
        
        this.components.set('tooltipClickHandler', clickHandler);
    }

    /**
     * Handle status panel click events
     * @private
     * @param {Event} e - Click event
     */
    handleStatusPanelClick(e) {
        // Handle panel-level clicks (e.g., opening settings)
        if (e.target.closest('.mcp-tools-status') && !e.target.closest('.mcp-tools-tooltip')) {
            // Could open settings or show more info
            console.log('[UIManager] Status panel clicked');
        }
    }

    /**
     * Handle tooltip click events
     * @private
     * @param {Event} e - Click event
     */
    handleTooltipClick(e) {
        const toolItem = e.target.closest('.tool-item');
        const moreTools = e.target.closest('.more-tools');

        if (toolItem) {
            const serverId = toolItem.dataset.serverId;
            const toolName = toolItem.dataset.toolName;
            if (serverId && toolName) {
                this.openServerDetails(serverId, toolName);
            }
        } else if (moreTools) {
            this.openServerDetails();
        }
    }

    /**
     * Update connection status display
     * @param {boolean} isConnected - Connection status
     */
    updateConnectionStatus(isConnected = null) {
        if (isConnected === null) {
            isConnected = this.stateManager.getState('isConnected');
        }
        
        const isConnecting = this.stateManager.getState('isConnecting');
        
        const connectionDot = document.getElementById('mcp-connection-dot');
        const connectionText = document.getElementById('mcp-connection-text');

        if (!connectionDot || !connectionText) return;

        let state, text;
        if (isConnecting) {
            state = 'connecting';
            text = 'Connecting...';
        } else if (isConnected) {
            state = 'connected';
            text = 'Connected';
        } else {
            state = 'disconnected';
            text = 'Disconnected';
        }

        connectionDot.className = `status-indicator ${state}`;
        connectionText.textContent = text;
        connectionText.className = `status-text ${state}`;

        if (this.client?.settings?.debugLogging) {
            console.log(`[UIManager] Updated connection status: ${state}`);
        }
    }

    /**
     * Update tools display
     * @param {Array} servers - MCP servers array
     */
    updateToolsDisplay(servers = null) {
        if (servers === null) {
            servers = this.stateManager.getState('mcpServers');
        }

        const toolsCountBadge = document.getElementById('mcp-tools-count-badge');
        const toolsList = document.getElementById('mcp-tooltip-tools-list');

        if (!toolsCountBadge || !toolsList) return;

        // Update tools count
        const toolCount = this.getConnectedToolsCount(servers);
        toolsCountBadge.textContent = `${toolCount} MCP tools available`;

        // Update tooltip content
        this.updateTooltipContent(toolsList, servers);
    }

    /**
     * Get count of connected tools
     * @private
     * @param {Array} servers - MCP servers array
     * @returns {number} Tool count
     */
    getConnectedToolsCount(servers) {
        let totalTools = 0;
        const settings = this.stateManager.getState('settings');
        
        for (const server of servers) {
            const serverSetting = settings.serverSettings?.[server.id];
            const serverEnabled = serverSetting?.enabled !== false;

            if ((server.status === 'running' || server.status === 'connected') && 
                server.tools && serverEnabled) {
                totalTools += server.tools.length;
            }
        }
        
        return totalTools;
    }

    /**
     * Update tooltip content
     * @private
     * @param {Element} toolsList - Tools list element
     * @param {Array} servers - MCP servers array
     */
    updateTooltipContent(toolsList, servers) {
        if (servers.length === 0) {
            toolsList.innerHTML = '<div class="no-tools">No servers connected</div>';
            return;
        }

        const allTools = this.collectAvailableTools(servers);

        if (allTools.length === 0) {
            toolsList.innerHTML = '<div class="no-tools">No tools available</div>';
            return;
        }

        // Limit to first 10 tools and add ellipsis if more
        const displayTools = allTools.slice(0, 10);
        const hasMore = allTools.length > 10;

        const toolsHTML = displayTools.map(tool => {
            const shortDesc = tool.description.length > 50
                ? tool.description.substring(0, 50) + '...'
                : tool.description;
            
            return `
                <div class="tool-item" data-server-id="${tool.serverId}" data-tool-name="${tool.toolName}" style="cursor: pointer;">
                    <div class="tool-name">${this.escapeHtml(tool.toolName)}</div>
                    <div class="tool-server">${this.escapeHtml(tool.serverName)}</div>
                    <div class="tool-description">${this.escapeHtml(shortDesc)}</div>
                </div>
            `;
        }).join('');

        const moreToolsHTML = hasMore 
            ? `<div class="more-tools" style="cursor: pointer;">... and ${allTools.length - 10} more tools</div>`
            : '';

        toolsList.innerHTML = toolsHTML + moreToolsHTML;

        // Re-setup event listeners after content update
        this.setupTooltipEventListeners();
    }

    /**
     * Collect available tools from servers
     * @private
     * @param {Array} servers - MCP servers array
     * @returns {Array} Available tools array
     */
    collectAvailableTools(servers) {
        const allTools = [];
        const settings = this.stateManager.getState('settings');

        servers.forEach(server => {
            const serverSetting = settings.serverSettings?.[server.id];
            const serverEnabled = serverSetting?.enabled !== false;

            if (server.tools && 
                (server.status === 'connected' || server.status === 'running') && 
                serverEnabled) {
                server.tools.forEach(tool => {
                    allTools.push({
                        serverId: server.id,
                        serverName: server.name || server.id,
                        toolName: tool.name,
                        description: tool.description || 'No description'
                    });
                });
            }
        });

        return allTools;
    }

    /**
     * Update status panel visibility
     * @param {boolean} show - Whether to show the panel
     */
    updateStatusPanelVisibility(show) {
        const statusPanel = this.components.get('statusPanel');
        if (statusPanel) {
            statusPanel.style.display = show ? 'flex' : 'none';
        }
    }

    /**
     * Update status panel position
     * @param {string} position - Panel position
     */
    updateStatusPanelPosition(position = null) {
        if (position === null) {
            position = this.stateManager.getState('settings.panelPosition');
        }

        const statusPanel = this.components.get('statusPanel');
        if (!statusPanel) return;

        // Remove existing position classes
        statusPanel.className = statusPanel.className
            .replace(/mcp-status-(top|bottom)-(left|right)/g, '');

        // Add new position class
        statusPanel.classList.add(`mcp-status-${position}`);
    }

    /**
     * Show visual feedback for actions
     * @param {Element} element - Element to show feedback on
     * @param {string} message - Feedback message
     * @param {string} type - Feedback type (success, error, info)
     */
    showFeedback(element, message, type = 'info') {
        const originalTitle = element.title;
        const originalOpacity = element.style.opacity;

        // Apply visual feedback
        element.style.opacity = '0.7';
        element.title = message;

        // Add CSS class for feedback type
        element.classList.add(`mcp-feedback-${type}`);

        // Reset after delay
        setTimeout(() => {
            element.style.opacity = originalOpacity;
            element.title = originalTitle;
            element.classList.remove(`mcp-feedback-${type}`);
        }, 2000);
    }

    /**
     * Open server details in settings
     * @param {string} serverId - Server ID (optional)
     * @param {string} toolId - Tool ID (optional)
     */
    openServerDetails(serverId = null, toolId = null) {
        try {
            const baseUrl = chrome.runtime.getURL('settings.html');
            let settingsUrl = baseUrl;

            if (serverId) {
                const encodedServerId = encodeURIComponent(serverId);
                if (toolId) {
                    const encodedToolId = encodeURIComponent(toolId);
                    settingsUrl = `${baseUrl}#/servers/${encodedServerId}/tools/${encodedToolId}`;
                } else {
                    settingsUrl = `${baseUrl}#/servers/${encodedServerId}`;
                }
            }

            chrome.tabs.create({ url: settingsUrl });
        } catch (error) {
            console.error('[UIManager] Failed to open server details:', error);
        }
    }

    /**
     * Escape HTML to prevent XSS
     * @private
     * @param {string} text - Text to escape
     * @returns {string} Escaped text
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Queue UI update to prevent excessive updates
     * @param {Function} updateFunction - Update function to queue
     */
    queueUpdate(updateFunction) {
        this.updateQueue.push(updateFunction);
        
        if (!this.isUpdating) {
            this.processUpdateQueue();
        }
    }

    /**
     * Process queued UI updates
     * @private
     */
    async processUpdateQueue() {
        this.isUpdating = true;
        
        while (this.updateQueue.length > 0) {
            const updateFunction = this.updateQueue.shift();
            try {
                await updateFunction();
            } catch (error) {
                console.error('[UIManager] Error processing update:', error);
            }
        }
        
        this.isUpdating = false;
    }

    /**
     * Get UI component by ID
     * @param {string} componentId - Component ID
     * @returns {Element|null} Component element
     */
    getComponent(componentId) {
        return this.components.get(componentId) || null;
    }

    /**
     * Cleanup UI manager
     */
    cleanup() {
        console.log('[UIManager] Starting cleanup...');
        
        try {
            // Remove all components
            this.components.forEach((component, id) => {
                if (component instanceof Element) {
                    component.remove();
                } else if (typeof component === 'string') {
                    // Event handler ID
                    this.eventManager.removeEventListener(component);
                }
            });
            
            this.components.clear();
            this.updateQueue.length = 0;
            this.isUpdating = false;
            this.isInitialized = false;
            
            console.log('[UIManager] Cleanup completed');
        } catch (error) {
            console.error('[UIManager] Error during cleanup:', error);
        }
    }

    /**
     * Get UI manager statistics
     * @returns {Object} UI statistics
     */
    getStats() {
        return {
            isInitialized: this.isInitialized,
            componentsCount: this.components.size,
            queuedUpdates: this.updateQueue.length,
            isUpdating: this.isUpdating
        };
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = UIManager;
} else {
    window.UIManager = UIManager;
}
