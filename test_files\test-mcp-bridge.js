// test-mcp-bridge.js
import fetch from 'node-fetch';

const BRIDGE_URL = 'http://localhost:54320';
const colors = {
    green: '\x1b[32m',
    red: '\x1b[31m',
    yellow: '\x1b[33m',
    blue: '\x1b[34m',
    reset: '\x1b[0m',
    bold: '\x1b[1m'
};

function log(message, color = 'reset') {
    console.log(`${colors[color]}${message}${colors.reset}`);
}

function logSection(title) {
    console.log(`\n${colors.bold}${colors.blue}=== ${title} ===${colors.reset}`);
}

async function makeRequest(endpoint, method = 'GET', body = null) {
    try {
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json'
            }
        };

        if (body) {
            options.body = JSON.stringify(body);
        }

        const response = await fetch(`${BRIDGE_URL}${endpoint}`, options);
        const data = await response.json();

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${data.error || 'Unknown error'}`);
        }

        return data;
    } catch (error) {
        log(`❌ Request failed: ${error.message}`, 'red');
        throw error;
    }
}

async function testHealthCheck() {
    logSection('Health Check');
    try {
        const health = await makeRequest('/health');
        log(`✅ Bridge Status: ${health.status}`, 'green');
        log(`📊 Connected Clients: ${health.connectedClients}`);
        log(`🔌 WebSocket Port: ${health.port}`);
        log(`⏰ Timestamp: ${health.timestamp}`);

        health.servers.forEach(server => {
            log(`📋 Server: ${server.name} (${server.status}) - ${server.tools} tools`);
        });

        return true;
    } catch (error) {
        log('❌ Health check failed', 'red');
        return false;
    }
}

async function testServerList() {
    logSection('Server List');
    try {
        const result = await makeRequest('/api/servers');

        if (result.success) {
            log(`✅ Found ${result.servers.length} server(s)`, 'green');

            result.servers.forEach(server => {
                log(`\n📋 Server: ${server.name}`, 'bold');
                log(`   ID: ${server.id}`);
                log(`   Type: ${server.type}`);
                log(`   Status: ${server.status}`);
                log(`   Tools: ${server.tools.length}`);

                if (server.tools.length > 0) {
                    log('   Available Tools:', 'yellow');
                    server.tools.forEach((tool, index) => {
                        log(`     ${index + 1}. ${tool.name}`);
                        log(`        Description: ${tool.description.substring(0, 100)}...`);

                        if (tool.inputSchema?.properties) {
                            const params = Object.keys(tool.inputSchema.properties);
                            log(`        Parameters: ${params.join(', ')}`);

                            if (tool.inputSchema.required) {
                                log(`        Required: ${tool.inputSchema.required.join(', ')}`);
                            }
                        }
                    });
                }
            });

            return result.servers;
        } else {
            log('❌ Failed to get server list', 'red');
            return [];
        }
    } catch (error) {
        log('❌ Server list test failed', 'red');
        return [];
    }
}

async function testResolveLibraryId(libraryName = 'react') {
    logSection(`Resolve Library ID - ${libraryName}`);
    try {
        const body = {
            server: 'context7',
            tool: 'resolve-library-id',
            arguments: {
                libraryName: libraryName
            }
        };

        log(`🔍 Resolving library: ${libraryName}`, 'yellow');
        const result = await makeRequest('/api/tools/call', 'POST', body);

        if (result.success) {
            log('✅ Library resolved successfully', 'green');
            log('📄 Result:', 'bold');

            // Try to parse the result and extract library ID
            if (result.result && Array.isArray(result.result)) {
                result.result.forEach(item => {
                    if (item.type === 'text') {
                        console.log(item.text);

                        // Try to extract library ID from the response
                        const idMatch = item.text.match(/\/[\w-]+\/[\w.-]+/);
                        if (idMatch) {
                            log(`🆔 Extracted Library ID: ${idMatch[0]}`, 'green');
                            return idMatch[0];
                        }
                    }
                });
            } else {
                console.log(JSON.stringify(result.result, null, 2));
            }

            return result.result;
        } else {
            log(`❌ Failed to resolve library: ${result.error}`, 'red');
            return null;
        }
    } catch (error) {
        log('❌ Resolve library ID test failed', 'red');
        return null;
    }
}

async function testGetLibraryDocs(libraryId, topic = 'getting started', tokens = 3000) {
    logSection(`Get Library Documentation`);
    try {
        const body = {
            server: 'context7',
            tool: 'get-library-docs',
            arguments: {
                context7CompatibleLibraryID: libraryId,
                topic: topic,
                tokens: tokens
            }
        };

        log(`📚 Getting docs for: ${libraryId}`, 'yellow');
        log(`🎯 Topic: ${topic}`);
        log(`🔢 Max tokens: ${tokens}`);

        const result = await makeRequest('/api/tools/call', 'POST', body);

        if (result.success) {
            log('✅ Documentation retrieved successfully', 'green');
            log('📄 Documentation:', 'bold');

            if (result.result && Array.isArray(result.result)) {
                result.result.forEach(item => {
                    if (item.type === 'text') {
                        // Show first 500 characters of documentation
                        const preview = item.text.substring(0, 500);
                        console.log(preview + (item.text.length > 500 ? '...' : ''));
                        log(`\n📊 Full length: ${item.text.length} characters`, 'blue');
                    }
                });
            } else {
                console.log(JSON.stringify(result.result, null, 2));
            }

            return result.result;
        } else {
            log(`❌ Failed to get documentation: ${result.error}`, 'red');
            return null;
        }
    } catch (error) {
        log('❌ Get library docs test failed', 'red');
        return null;
    }
}

async function testFullWorkflow(libraryName = 'vue') {
    logSection(`Full Workflow Test - ${libraryName}`);

    try {
        // Step 1: Resolve library ID
        log(`🔄 Step 1: Resolving ${libraryName}...`, 'yellow');
        const resolveResult = await testResolveLibraryId(libraryName);

        if (!resolveResult) {
            log('❌ Cannot proceed without library ID', 'red');
            return false;
        }

        // Extract library ID from result
        let libraryId = null;
        if (Array.isArray(resolveResult)) {
            for (const item of resolveResult) {
                if (item.type === 'text') {
                    const idMatch = item.text.match(/\/[\w-]+\/[\w.-]+/);
                    if (idMatch) {
                        libraryId = idMatch[0];
                        break;
                    }
                }
            }
        }

        // Fallback library IDs for common libraries
        if (!libraryId) {
            const fallbackIds = {
                'vue': '/vuejs/core',
                'react': '/facebook/react',
                'nextjs': '/vercel/next.js',
                'next': '/vercel/next.js',
                'express': '/expressjs/express',
                'mongodb': '/mongodb/docs'
            };
            libraryId = fallbackIds[libraryName.toLowerCase()] || `/org/${libraryName}`;
            log(`⚠️  Using fallback library ID: ${libraryId}`, 'yellow');
        }

        // Step 2: Get documentation
        log(`🔄 Step 2: Getting documentation for ${libraryId}...`, 'yellow');
        const docsResult = await testGetLibraryDocs(libraryId, 'components', 2000);

        if (docsResult) {
            log('✅ Full workflow completed successfully', 'green');
            return true;
        } else {
            log('❌ Documentation retrieval failed', 'red');
            return false;
        }

    } catch (error) {
        log(`❌ Full workflow failed: ${error.message}`, 'red');
        return false;
    }
}

async function runAllTests() {
    log('🚀 Starting MCP Bridge Tests', 'bold');
    log('================================\n');

    const results = {
        health: false,
        serverList: false,
        resolveLibrary: false,
        fullWorkflow: false
    };

    // Test 1: Health Check
    results.health = await testHealthCheck();

    if (!results.health) {
        log('\n❌ Bridge is not healthy, stopping tests', 'red');
        return results;
    }

    // Test 2: Server List
    const servers = await testServerList();
    results.serverList = servers.length > 0;

    if (!results.serverList) {
        log('\n❌ No servers available, stopping tests', 'red');
        return results;
    }

    // Test 3: Individual tool test
    results.resolveLibrary = (await testResolveLibraryId('typescript')) !== null;

    // Test 4: Full workflow
    results.fullWorkflow = await testFullWorkflow('react');

    // Summary
    logSection('Test Summary');
    Object.entries(results).forEach(([test, passed]) => {
        const status = passed ? '✅ PASSED' : '❌ FAILED';
        const color = passed ? 'green' : 'red';
        log(`${test.padEnd(15)}: ${status}`, color);
    });

    const passedCount = Object.values(results).filter(Boolean).length;
    const totalCount = Object.keys(results).length;

    log(`\n📊 Overall: ${passedCount}/${totalCount} tests passed`,
        passedCount === totalCount ? 'green' : 'yellow');

    return results;
}

// CLI interface
const args = process.argv.slice(2);

if (args.length === 0) {
    console.log(`
Usage: node test-mcp-bridge.js [command] [args...]

Commands:
  health                    - Test health endpoint
  servers                   - List all servers and tools
  resolve <library>         - Resolve library ID
  docs <libraryId> [topic]  - Get documentation
  workflow <library>        - Test full workflow
  all                       - Run all tests

Examples:
  node test-mcp-bridge.js all
  node test-mcp-bridge.js resolve react
  node test-mcp-bridge.js docs /facebook/react hooks
  node test-mcp-bridge.js workflow vue
`);
    process.exit(0);
}

const command = args[0];

switch (command) {
    case 'health':
        await testHealthCheck();
        break;
    case 'servers':
        await testServerList();
        break;
    case 'resolve':
        await testResolveLibraryId(args[1] || 'react');
        break;
    case 'docs':
        await testGetLibraryDocs(args[1] || '/facebook/react', args[2] || 'hooks');
        break;
    case 'workflow':
        await testFullWorkflow(args[1] || 'vue');
        break;
    case 'all':
        await runAllTests();
        break;
    default:
        log(`❌ Unknown command: ${command}`, 'red');
        process.exit(1);
}
