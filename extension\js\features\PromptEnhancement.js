/**
 * PromptEnhancement - Manages prompt injection and enhancement
 * 
 * Handles MCP system prompt generation, injection monitoring,
 * and query cleanup for seamless user experience.
 * 
 * @version 2.0.0
 */

class PromptEnhancement {
    constructor() {
        this.isInitialized = false;
        this.isMonitoring = false;
        this.lastUserPrompt = '';
        this.cleanupObserver = null;
        this.enhancementStats = {
            totalEnhancements: 0,
            successfulEnhancements: 0,
            cleanupOperations: 0
        };
    }

    /**
     * Initialize prompt enhancement manager
     * @param {Object} client - Reference to the main MCP client
     * @param {StateManager} stateManager - State manager instance
     * @param {ObserverManager} observerManager - Observer manager instance
     * @param {ErrorHandler} errorHandler - Error handler instance
     */
    initialize(client, stateManager, observerManager, errorHandler) {
        this.client = client;
        this.stateManager = stateManager;
        this.observerManager = observerManager;
        this.errorHandler = errorHandler;
        
        this.isInitialized = true;
        console.log('[PromptEnhancement] Initialized successfully');
    }

    /**
     * Start prompt enhancement monitoring
     */
    startMonitoring() {
        if (this.isMonitoring) {
            console.log('[PromptEnhancement] Already monitoring');
            return;
        }

        console.log('[PromptEnhancement] Starting prompt enhancement monitoring...');
        
        try {
            this.setupPromptInputMonitoring();
            this.setupTextareaAppearanceMonitoring();
            this.isMonitoring = true;
            
            console.log('[PromptEnhancement] Monitoring started successfully');
        } catch (error) {
            this.errorHandler?.handleError(error, 'start_prompt_monitoring');
        }
    }

    /**
     * Set up prompt input monitoring
     * @private
     */
    setupPromptInputMonitoring() {
        // Create observer for prompt input changes
        this.observerManager?.createMutationObserver(
            'promptInputMonitor',
            (mutations) => this.processPromptInputMutations(mutations),
            {
                childList: true,
                subtree: true,
                attributes: true,
                attributeFilter: ['value']
            },
            50 // Fast response for input monitoring
        );

        // Start observing the document
        this.observerManager?.startMutationObserver('promptInputMonitor', document.body);
    }

    /**
     * Set up textarea appearance monitoring
     * @private
     */
    setupTextareaAppearanceMonitoring() {
        // Create observer for textarea appearance
        this.observerManager?.createMutationObserver(
            'textareaAppearanceMonitor',
            (mutations) => this.processTextareaAppearanceMutations(mutations),
            {
                childList: true,
                subtree: true
            },
            100 // Moderate debouncing for appearance changes
        );

        // Start observing the document
        this.observerManager?.startMutationObserver('textareaAppearanceMonitor', document.body);
    }

    /**
     * Process prompt input mutations
     * @private
     * @param {MutationRecord[]} mutations - Array of mutation records
     */
    processPromptInputMutations(mutations) {
        try {
            for (const mutation of mutations) {
                if (mutation.type === 'childList') {
                    for (const node of mutation.addedNodes) {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            this.checkForPromptInput(node);
                        }
                    }
                }
            }
        } catch (error) {
            this.errorHandler?.handleError(error, 'process_prompt_input_mutations');
        }
    }

    /**
     * Process textarea appearance mutations
     * @private
     * @param {MutationRecord[]} mutations - Array of mutation records
     */
    processTextareaAppearanceMutations(mutations) {
        try {
            for (const mutation of mutations) {
                if (mutation.type === 'childList') {
                    for (const node of mutation.addedNodes) {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            this.checkForTextareaAppearance(node);
                        }
                    }
                }
            }
        } catch (error) {
            this.errorHandler?.handleError(error, 'process_textarea_appearance_mutations');
        }
    }

    /**
     * Check for prompt input elements
     * @private
     * @param {Element} node - DOM node to check
     */
    checkForPromptInput(node) {
        const textarea = node.matches?.('textarea#ask-input') 
            ? node 
            : node.querySelector?.('textarea#ask-input');
            
        if (textarea && !this.client.promptInput) {
            this.client.promptInput = textarea;
            this.setupPromptInputHandlers(textarea);
        }
    }

    /**
     * Check for textarea appearance
     * @private
     * @param {Element} node - DOM node to check
     */
    checkForTextareaAppearance(node) {
        const textarea = node.matches?.('textarea#ask-input') 
            ? node 
            : node.querySelector?.('textarea#ask-input');
            
        if (textarea) {
            // Reinitialize seamless mode if needed
            if (!this.client.settings?.legacyMode && !this.client.seamlessMode?.userTextarea) {
                setTimeout(() => {
                    this.client.initializeSeamlessMode?.();
                }, 500);
            }
        }
    }

    /**
     * Set up prompt input handlers
     * @private
     * @param {HTMLTextAreaElement} textarea - Textarea element
     */
    setupPromptInputHandlers(textarea) {
        // Handle Enter key for prompt submission
        const keydownHandler = (e) => {
            if (e.key === 'Enter' && !e.shiftKey) {
                const userPrompt = textarea.value.trim();
                if (userPrompt && this.shouldEnhancePrompt(userPrompt)) {
                    e.preventDefault();
                    this.handlePromptSubmission(userPrompt, textarea);
                }
            }
        };

        textarea.addEventListener('keydown', keydownHandler);
        
        // Store handler for cleanup
        this.promptInputHandler = keydownHandler;
    }

    /**
     * Handle prompt submission with enhancement
     * @private
     * @param {string} userPrompt - User's prompt
     * @param {HTMLTextAreaElement} textarea - Textarea element
     */
    handlePromptSubmission(userPrompt, textarea) {
        try {
            console.log('[PromptEnhancement] Handling enhanced prompt submission');
            
            const systemPrompt = this.generateMcpSystemPrompt();
            if (!systemPrompt) {
                // No enhancement needed, submit normally
                this.client.submitTextInBackground?.(textarea);
                return;
            }

            // Create enhanced prompt
            const enhancedPrompt = `${userPrompt}${systemPrompt}`;
            
            // Store original for cleanup
            this.lastUserPrompt = userPrompt;
            
            // Set enhanced prompt
            this.client.sendTextInBackground?.(textarea, enhancedPrompt);
            
            // Start cleanup monitoring
            this.startRealtimeQueryCleanup(userPrompt);
            
            // Submit after brief delay
            setTimeout(() => {
                this.client.submitTextInBackground?.(textarea);
            }, 100);
            
            this.enhancementStats.totalEnhancements++;
            this.enhancementStats.successfulEnhancements++;
            
        } catch (error) {
            this.errorHandler?.handleError(error, 'handle_prompt_submission', { userPrompt });
        }
    }

    /**
     * Generate MCP system prompt
     * @returns {string|null} System prompt or null if no tools available
     */
    generateMcpSystemPrompt() {
        try {
            const servers = this.stateManager?.getState('mcpServers') || [];
            const settings = this.stateManager?.getState('settings') || {};
            
            if (!servers.length) {
                return null;
            }

            const availableTools = this.collectAvailableTools(servers, settings);
            if (!availableTools.length) {
                return null;
            }

            // Generate system prompt
            const toolDescriptions = availableTools.map(tool => 
                `- ${tool.name}: ${tool.description || 'No description available'}`
            ).join('\n');

            return `\n\n--------------------------------\nMCP TOOLS ENHANCEMENT\n--------------------------------\n\nAvailable MCP Tools:\n${toolDescriptions}\n\nTo use a tool, include in your response:\n<mcp_tool server="server_id" tool="tool_name" parameters="{}">optional_content</mcp_tool>\n\nExample:\n<mcp_tool server="filesystem" tool="read_file" parameters="{\\"path\\": \\"/path/to/file\\"}"></mcp_tool>\n\n--------------------------------`;
        } catch (error) {
            this.errorHandler?.handleError(error, 'generate_mcp_system_prompt');
            return null;
        }
    }

    /**
     * Collect available tools from servers
     * @private
     * @param {Array} servers - MCP servers
     * @param {Object} settings - Current settings
     * @returns {Array} Available tools
     */
    collectAvailableTools(servers, settings) {
        const tools = [];
        
        for (const server of servers) {
            const serverSetting = settings.serverSettings?.[server.id];
            const serverEnabled = serverSetting?.enabled !== false;
            
            if (server.tools && 
                (server.status === 'connected' || server.status === 'running') && 
                serverEnabled) {
                
                server.tools.forEach(tool => {
                    tools.push({
                        name: tool.name,
                        description: tool.description,
                        server: server.id
                    });
                });
            }
        }
        
        return tools;
    }

    /**
     * Check if prompt should be enhanced
     * @param {string} prompt - User prompt
     * @returns {boolean} True if should enhance
     */
    shouldEnhancePrompt(prompt) {
        try {
            if (!prompt || !prompt.trim()) {
                return false;
            }

            // Skip if already enhanced
            if (prompt.includes('MCP TOOLS ENHANCEMENT') || 
                prompt.includes('Available MCP Tools')) {
                return false;
            }

            // Skip tool results
            if (prompt.startsWith('[MCP Tool Result from')) {
                return false;
            }

            // Check settings
            const settings = this.stateManager?.getState('settings') || {};
            if (!settings.bridgeEnabled) {
                return false;
            }

            // Check if we have enabled servers
            const servers = this.stateManager?.getState('mcpServers') || [];
            const hasEnabledServers = servers.some(server => {
                const serverSetting = settings.serverSettings?.[server.id];
                const isEnabled = serverSetting?.enabled !== false;
                return isEnabled && (server.status === 'connected' || server.status === 'running');
            });

            return hasEnabledServers;
        } catch (error) {
            this.errorHandler?.handleError(error, 'should_enhance_prompt', { prompt });
            return false;
        }
    }

    /**
     * Start real-time query cleanup monitoring
     * @param {string} originalUserPrompt - Original user prompt
     */
    startRealtimeQueryCleanup(originalUserPrompt) {
        if (!originalUserPrompt || this.cleanupObserver) {
            return;
        }

        console.log('[PromptEnhancement] Starting real-time query cleanup monitoring');

        let lastCleanupActivity = Date.now();
        let hasFoundTargetElement = false;
        const inactivityTimeout = 60000; // 1 minute

        // Create observer for query cleanup
        this.observerManager?.createMutationObserver(
            'queryCleanupMonitor',
            (mutations) => {
                let activityDetected = false;

                for (const mutation of mutations) {
                    if (mutation.type === 'childList') {
                        for (const addedNode of mutation.addedNodes) {
                            if (addedNode.nodeType === Node.ELEMENT_NODE) {
                                const cleanupResult = this.checkAndCleanupQueryElement(addedNode, originalUserPrompt);
                                if (cleanupResult) {
                                    activityDetected = true;
                                    hasFoundTargetElement = true;
                                }
                            }
                        }
                    }
                }

                if (activityDetected) {
                    lastCleanupActivity = Date.now();
                }
            },
            {
                childList: true,
                subtree: true
            }
        );

        // Start observing
        this.observerManager?.startMutationObserver('queryCleanupMonitor', document.body);

        // Store original prompt for reference
        this.lastUserPrompt = originalUserPrompt;

        // Set up cleanup timer
        const cleanupTimer = setInterval(() => {
            const timeSinceLastActivity = Date.now() - lastCleanupActivity;
            
            if (timeSinceLastActivity > inactivityTimeout || hasFoundTargetElement) {
                this.observerManager?.disconnectObserver('queryCleanupMonitor');
                clearInterval(cleanupTimer);
                
                const reason = hasFoundTargetElement ? 'target found and cleaned' : 'inactivity timeout';
                console.log(`[PromptEnhancement] Query cleanup monitoring stopped: ${reason}`);
            }
        }, 5000);
    }

    /**
     * Check and cleanup query element
     * @private
     * @param {Element} element - Element to check
     * @param {string} originalPrompt - Original user prompt
     * @returns {boolean} True if cleanup was performed
     */
    checkAndCleanupQueryElement(element, originalPrompt) {
        try {
            // Check if this is a query element or contains query elements
            const querySelectors = [
                '.pt-md.md\\:pt-lg>div>div>div>div>div>div',
                'div[data-testid="answer-mode-tabs"]>div>div.hidden'
            ];

            for (const selector of querySelectors) {
                const queryElements = element.matches?.(selector) 
                    ? [element] 
                    : Array.from(element.querySelectorAll?.(selector) || []);

                for (const queryElement of queryElements) {
                    if (this.cleanupQueryElement(queryElement, originalPrompt)) {
                        this.enhancementStats.cleanupOperations++;
                        return true;
                    }
                }
            }

            return false;
        } catch (error) {
            this.errorHandler?.handleError(error, 'check_and_cleanup_query_element', { element, originalPrompt });
            return false;
        }
    }

    /**
     * Cleanup individual query element
     * @private
     * @param {Element} element - Query element to cleanup
     * @param {string} originalPrompt - Original user prompt
     * @returns {boolean} True if cleanup was performed
     */
    cleanupQueryElement(element, originalPrompt) {
        try {
            const contentElement = element.children?.[0] || element;
            const currentText = contentElement.textContent || '';

            // Check if the text contains enhancement markers
            if (currentText.includes('--------------------------------') ||
                currentText.includes('MCP TOOLS ENHANCEMENT') ||
                currentText.includes('Available MCP Tools')) {

                console.log('[PromptEnhancement] Found enhanced query, replacing with original');
                
                // Replace with original prompt
                contentElement.textContent = originalPrompt;
                
                // Set height to auto to prevent layout issues
                contentElement.style.setProperty('height', 'auto', 'important');
                
                return true;
            }

            return false;
        } catch (error) {
            this.errorHandler?.handleError(error, 'cleanup_query_element', { element, originalPrompt });
            return false;
        }
    }

    /**
     * Stop monitoring
     */
    stopMonitoring() {
        if (!this.isMonitoring) {
            return;
        }

        console.log('[PromptEnhancement] Stopping monitoring...');
        
        try {
            // Disconnect observers
            this.observerManager?.disconnectObserver('promptInputMonitor');
            this.observerManager?.disconnectObserver('textareaAppearanceMonitor');
            this.observerManager?.disconnectObserver('queryCleanupMonitor');
            
            // Remove event handlers
            if (this.client.promptInput && this.promptInputHandler) {
                this.client.promptInput.removeEventListener('keydown', this.promptInputHandler);
            }
            
            this.isMonitoring = false;
            console.log('[PromptEnhancement] Monitoring stopped');
        } catch (error) {
            this.errorHandler?.handleError(error, 'stop_monitoring');
        }
    }

    /**
     * Get enhancement statistics
     * @returns {Object} Enhancement statistics
     */
    getStats() {
        return {
            ...this.enhancementStats,
            isInitialized: this.isInitialized,
            isMonitoring: this.isMonitoring,
            lastUserPrompt: this.lastUserPrompt
        };
    }

    /**
     * Cleanup prompt enhancement manager
     */
    cleanup() {
        console.log('[PromptEnhancement] Starting cleanup...');
        
        try {
            this.stopMonitoring();
            this.lastUserPrompt = '';
            this.isInitialized = false;
            
            console.log('[PromptEnhancement] Cleanup completed');
        } catch (error) {
            console.error('[PromptEnhancement] Error during cleanup:', error);
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = PromptEnhancement;
} else {
    window.PromptEnhancement = PromptEnhancement;
}
