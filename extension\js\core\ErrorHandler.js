/**
 * <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> - Centralized error handling and reporting
 * 
 * Provides comprehensive error handling, logging, and user notification
 * with proper error boundaries and recovery mechanisms.
 * 
 * @version 2.0.0
 */

class ErrorHandler {
    constructor() {
        this.errorLog = [];
        this.maxLogSize = 100;
        this.isInitialized = false;
        this.errorCounts = new Map();
        this.suppressedErrors = new Set();
        this.errorCallbacks = new Map();
    }

    /**
     * Initialize the error handler
     * @param {Object} client - Reference to the main MCP client
     */
    initialize(client) {
        this.client = client;
        this.setupGlobalErrorHandlers();
        this.isInitialized = true;
        console.log('[<PERSON>rror<PERSON>and<PERSON>] Initialized successfully');
    }

    /**
     * Set up global error handlers
     * @private
     */
    setupGlobalErrorHandlers() {
        // Handle unhandled promise rejections
        window.addEventListener('unhandledrejection', (event) => {
            this.handleError(event.reason, 'unhandledrejection', {
                promise: event.promise,
                type: 'promise'
            });
        });

        // Handle general errors
        window.addEventListener('error', (event) => {
            this.handleError(event.error || event.message, 'error', {
                filename: event.filename,
                lineno: event.lineno,
                colno: event.colno,
                type: 'javascript'
            });
        });
    }

    /**
     * Handle an error with proper logging and user notification
     * @param {Error|string} error - The error to handle
     * @param {string} context - Context where error occurred
     * @param {Object} metadata - Additional error metadata
     * @param {boolean} showToUser - Whether to show error to user
     */
    handleError(error, context = 'unknown', metadata = {}, showToUser = false) {
        try {
            const errorInfo = this.createErrorInfo(error, context, metadata);
            
            // Log the error
            this.logError(errorInfo);
            
            // Check if this error should be suppressed
            if (this.shouldSuppressError(errorInfo)) {
                return;
            }
            
            // Update error counts
            this.updateErrorCounts(errorInfo);
            
            // Notify error callbacks
            this.notifyErrorCallbacks(errorInfo);
            
            // Show to user if requested or if critical
            if (showToUser || this.isCriticalError(errorInfo)) {
                this.showErrorToUser(errorInfo);
            }
            
            // Log to console with appropriate level
            this.logToConsole(errorInfo);
            
        } catch (handlerError) {
            // Fallback error handling
            console.error('[ErrorHandler] Error in error handler:', handlerError);
            console.error('[ErrorHandler] Original error:', error);
        }
    }

    /**
     * Create standardized error information object
     * @private
     * @param {Error|string} error - The error
     * @param {string} context - Error context
     * @param {Object} metadata - Additional metadata
     * @returns {Object} Error information object
     */
    createErrorInfo(error, context, metadata) {
        const timestamp = new Date().toISOString();
        const errorId = `${timestamp}_${Math.random().toString(36).substr(2, 9)}`;
        
        let message, stack, name;
        
        if (error instanceof Error) {
            message = error.message;
            stack = error.stack;
            name = error.name;
        } else {
            message = String(error);
            stack = new Error().stack;
            name = 'UnknownError';
        }
        
        return {
            id: errorId,
            timestamp,
            message,
            stack,
            name,
            context,
            metadata: { ...metadata },
            severity: this.determineSeverity(error, context),
            userAgent: navigator.userAgent,
            url: window.location.href
        };
    }

    /**
     * Determine error severity
     * @private
     * @param {Error|string} error - The error
     * @param {string} context - Error context
     * @returns {string} Severity level
     */
    determineSeverity(error, context) {
        const message = error instanceof Error ? error.message : String(error);
        const lowerMessage = message.toLowerCase();
        
        // Critical errors
        if (lowerMessage.includes('initialization') || 
            lowerMessage.includes('fatal') ||
            context.includes('init')) {
            return 'critical';
        }
        
        // High severity errors
        if (lowerMessage.includes('connection') ||
            lowerMessage.includes('network') ||
            lowerMessage.includes('timeout')) {
            return 'high';
        }
        
        // Medium severity errors
        if (lowerMessage.includes('validation') ||
            lowerMessage.includes('parsing') ||
            lowerMessage.includes('format')) {
            return 'medium';
        }
        
        // Default to low severity
        return 'low';
    }

    /**
     * Log error to internal log
     * @private
     * @param {Object} errorInfo - Error information
     */
    logError(errorInfo) {
        this.errorLog.push(errorInfo);
        
        // Maintain log size limit
        if (this.errorLog.length > this.maxLogSize) {
            this.errorLog.shift();
        }
    }

    /**
     * Check if error should be suppressed
     * @private
     * @param {Object} errorInfo - Error information
     * @returns {boolean} True if should be suppressed
     */
    shouldSuppressError(errorInfo) {
        const errorKey = `${errorInfo.context}:${errorInfo.message}`;
        return this.suppressedErrors.has(errorKey);
    }

    /**
     * Update error counts for rate limiting
     * @private
     * @param {Object} errorInfo - Error information
     */
    updateErrorCounts(errorInfo) {
        const errorKey = `${errorInfo.context}:${errorInfo.message}`;
        const count = this.errorCounts.get(errorKey) || 0;
        this.errorCounts.set(errorKey, count + 1);
        
        // Suppress if too many of the same error
        if (count > 5) {
            this.suppressedErrors.add(errorKey);
        }
    }

    /**
     * Notify registered error callbacks
     * @private
     * @param {Object} errorInfo - Error information
     */
    notifyErrorCallbacks(errorInfo) {
        this.errorCallbacks.forEach((callback, callbackId) => {
            try {
                callback(errorInfo);
            } catch (callbackError) {
                console.error(`[ErrorHandler] Error in callback ${callbackId}:`, callbackError);
            }
        });
    }

    /**
     * Check if error is critical
     * @private
     * @param {Object} errorInfo - Error information
     * @returns {boolean} True if critical
     */
    isCriticalError(errorInfo) {
        return errorInfo.severity === 'critical';
    }

    /**
     * Show error to user with appropriate UI
     * @private
     * @param {Object} errorInfo - Error information
     */
    showErrorToUser(errorInfo) {
        try {
            const errorElement = document.createElement('div');
            errorElement.className = 'mcp-error-notification';
            errorElement.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                background: ${this.getErrorColor(errorInfo.severity)};
                color: white;
                padding: 12px 16px;
                border-radius: 8px;
                z-index: 10000;
                font-family: system-ui, sans-serif;
                font-size: 14px;
                max-width: 350px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                border-left: 4px solid rgba(255,255,255,0.3);
            `;
            
            const title = this.getErrorTitle(errorInfo.severity);
            const message = this.sanitizeErrorMessage(errorInfo.message);
            
            errorElement.innerHTML = `
                <div style="font-weight: 600; margin-bottom: 4px;">${title}</div>
                <div style="font-size: 13px; opacity: 0.9;">${message}</div>
                <div style="font-size: 11px; opacity: 0.7; margin-top: 8px;">
                    Context: ${errorInfo.context} | ID: ${errorInfo.id.substr(-8)}
                </div>
            `;
            
            document.body?.appendChild(errorElement);
            
            // Auto-remove after delay based on severity
            const delay = this.getErrorDisplayDuration(errorInfo.severity);
            setTimeout(() => {
                errorElement.remove();
            }, delay);
            
        } catch (displayError) {
            console.error('[ErrorHandler] Could not display error to user:', displayError);
        }
    }

    /**
     * Get error color based on severity
     * @private
     * @param {string} severity - Error severity
     * @returns {string} CSS color
     */
    getErrorColor(severity) {
        const colors = {
            critical: '#dc2626',
            high: '#ea580c',
            medium: '#d97706',
            low: '#65a30d'
        };
        return colors[severity] || colors.low;
    }

    /**
     * Get error title based on severity
     * @private
     * @param {string} severity - Error severity
     * @returns {string} Error title
     */
    getErrorTitle(severity) {
        const titles = {
            critical: 'Critical Error',
            high: 'Error',
            medium: 'Warning',
            low: 'Notice'
        };
        return titles[severity] || titles.low;
    }

    /**
     * Get error display duration based on severity
     * @private
     * @param {string} severity - Error severity
     * @returns {number} Duration in milliseconds
     */
    getErrorDisplayDuration(severity) {
        const durations = {
            critical: 15000,
            high: 10000,
            medium: 7000,
            low: 5000
        };
        return durations[severity] || durations.low;
    }

    /**
     * Sanitize error message for display
     * @private
     * @param {string} message - Error message
     * @returns {string} Sanitized message
     */
    sanitizeErrorMessage(message) {
        return message
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .substring(0, 200);
    }

    /**
     * Log error to console with appropriate level
     * @private
     * @param {Object} errorInfo - Error information
     */
    logToConsole(errorInfo) {
        const prefix = `[ErrorHandler:${errorInfo.context}]`;
        const message = `${errorInfo.message} (ID: ${errorInfo.id})`;
        
        switch (errorInfo.severity) {
            case 'critical':
                console.error(prefix, message, errorInfo);
                break;
            case 'high':
                console.error(prefix, message);
                break;
            case 'medium':
                console.warn(prefix, message);
                break;
            case 'low':
                if (this.client?.settings?.debugLogging) {
                    console.log(prefix, message);
                }
                break;
        }
    }

    /**
     * Register an error callback
     * @param {string} callbackId - Unique callback identifier
     * @param {Function} callback - Callback function
     */
    registerErrorCallback(callbackId, callback) {
        this.errorCallbacks.set(callbackId, callback);
    }

    /**
     * Unregister an error callback
     * @param {string} callbackId - Callback identifier to remove
     */
    unregisterErrorCallback(callbackId) {
        this.errorCallbacks.delete(callbackId);
    }

    /**
     * Create an error boundary wrapper for functions
     * @param {Function} func - Function to wrap
     * @param {string} context - Error context
     * @returns {Function} Wrapped function
     */
    createErrorBoundary(func, context) {
        return (...args) => {
            try {
                const result = func.apply(this, args);
                
                // Handle async functions
                if (result instanceof Promise) {
                    return result.catch((error) => {
                        this.handleError(error, context, { args });
                        throw error;
                    });
                }
                
                return result;
            } catch (error) {
                this.handleError(error, context, { args });
                throw error;
            }
        };
    }

    /**
     * Get error log
     * @param {number} limit - Maximum number of errors to return
     * @returns {Array} Array of error information objects
     */
    getErrorLog(limit = 50) {
        return this.errorLog.slice(-limit);
    }

    /**
     * Clear error log
     */
    clearErrorLog() {
        this.errorLog.length = 0;
        this.errorCounts.clear();
        this.suppressedErrors.clear();
    }

    /**
     * Get error statistics
     * @returns {Object} Error statistics
     */
    getStats() {
        const severityCounts = { critical: 0, high: 0, medium: 0, low: 0 };
        
        this.errorLog.forEach(error => {
            severityCounts[error.severity]++;
        });
        
        return {
            totalErrors: this.errorLog.length,
            uniqueErrors: this.errorCounts.size,
            suppressedErrors: this.suppressedErrors.size,
            severityCounts,
            callbacksRegistered: this.errorCallbacks.size
        };
    }

    /**
     * Cleanup error handler
     */
    cleanup() {
        console.log('[ErrorHandler] Starting cleanup...');
        
        try {
            this.clearErrorLog();
            this.errorCallbacks.clear();
            this.isInitialized = false;
            console.log('[ErrorHandler] Cleanup completed');
        } catch (error) {
            console.error('[ErrorHandler] Error during cleanup:', error);
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ErrorHandler;
} else {
    window.ErrorHandler = ErrorHandler;
}
