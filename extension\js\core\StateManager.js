/**
 * StateManager - Centralized application state management
 * 
 * Manages application state, settings, and provides reactive
 * state updates with proper persistence and validation.
 * 
 * @version 2.0.0
 */

class StateManager {
    constructor() {
        this.state = this.getInitialState();
        this.listeners = new Map();
        this.isInitialized = false;
        this.persistenceKeys = new Set([
            'settings',
            'mcpServers',
            'seamlessMode.completedWidgetStates',
            'seamlessMode.cleanedOriginalPrompts',
            'seamlessMode.deletedToolCallResults'
        ]);
    }

    /**
     * Get initial state structure
     * @private
     * @returns {Object} Initial state object
     */
    getInitialState() {
        return {
            // Connection state
            isConnected: false,
            isConnecting: false,
            mcpServers: [],
            pendingRequests: new Map(),
            requestId: 0,

            // Settings
            settings: this.getDefaultSettings(),

            // UI state
            currentUrl: '',
            currentThreadId: null,
            lastUserPrompt: '',
            promptInput: null,

            // Seamless mode state
            seamlessMode: {
                activeToolCalls: new Map(),
                hiddenTextarea: null,
                userTextarea: null,
                responseElementCount: 0,
                pendingDeletions: [],
                completedWidgetStates: [],
                loadedCompletedWidgetStates: [],
                cleanedOriginalPrompts: [],
                loadedCleanedOriginalPrompts: [],
                deletedToolCallResults: [],
                loadedDeletedToolCallResults: [],
                lastPbLgCount: 0,
                resizeObserver: null,
                MAX_PROCESSING_ATTEMPTS: 3,
                PROCESSING_RETRY_DELAYS: [500, 1000, 1500]
            },

            // Session tracking
            restoredWidgetSources: new Set(),
            restoredCleanedQueries: new Set(),
            isSubmittingToolResult: false
        };
    }

    /**
     * Get default settings
     * @private
     * @returns {Object} Default settings object
     */
    getDefaultSettings() {
        return {
            bridgeEnabled: true,
            autoExecute: true,
            legacyMode: false,
            seamlessMode: true,
            debugLogging: false,
            showStatusPanel: true,
            panelPosition: 'bottom-right',
            showToolResults: true,
            resultStyle: 'enhanced',
            executionTimeout: 30000,
            serverSettings: {}
        };
    }

    /**
     * Initialize the state manager
     * @param {Object} client - Reference to the main MCP client
     * @async
     * @returns {Promise<void>}
     */
    async initialize(client) {
        this.client = client;
        
        try {
            await this.loadPersistedState();
            this.isInitialized = true;
            console.log('[StateManager] Initialized successfully');
        } catch (error) {
            console.error('[StateManager] Failed to initialize:', error);
            throw error;
        }
    }

    /**
     * Get current state or specific state path
     * @param {string} path - Optional dot-notation path to specific state
     * @returns {any} State value
     */
    getState(path = null) {
        if (!path) return { ...this.state };
        
        return this.getNestedValue(this.state, path);
    }

    /**
     * Set state with validation and notifications
     * @param {string|Object} pathOrState - Dot-notation path or state object
     * @param {any} value - Value to set (if path provided)
     * @param {boolean} persist - Whether to persist this change
     */
    setState(pathOrState, value = undefined, persist = false) {
        const oldState = { ...this.state };
        
        if (typeof pathOrState === 'string') {
            // Path-based update
            this.setNestedValue(this.state, pathOrState, value);
        } else {
            // Object merge update
            this.state = { ...this.state, ...pathOrState };
        }

        // Validate state changes
        this.validateState();

        // Notify listeners
        this.notifyListeners(oldState, this.state);

        // Persist if requested
        if (persist) {
            this.persistState(pathOrState);
        }
    }

    /**
     * Subscribe to state changes
     * @param {string} path - State path to watch (optional)
     * @param {Function} callback - Callback function
     * @returns {string} Subscription ID for unsubscribing
     */
    subscribe(path, callback) {
        if (typeof path === 'function') {
            // No path specified, watch all changes
            callback = path;
            path = '*';
        }

        const subscriptionId = `${path}_${Date.now()}_${Math.random()}`;
        
        if (!this.listeners.has(path)) {
            this.listeners.set(path, new Map());
        }
        
        this.listeners.get(path).set(subscriptionId, callback);
        
        return subscriptionId;
    }

    /**
     * Unsubscribe from state changes
     * @param {string} subscriptionId - Subscription ID
     */
    unsubscribe(subscriptionId) {
        for (const [path, callbacks] of this.listeners) {
            if (callbacks.has(subscriptionId)) {
                callbacks.delete(subscriptionId);
                if (callbacks.size === 0) {
                    this.listeners.delete(path);
                }
                break;
            }
        }
    }

    /**
     * Update settings with validation
     * @param {Object} newSettings - New settings to merge
     * @param {boolean} persist - Whether to persist changes
     */
    updateSettings(newSettings, persist = true) {
        const validatedSettings = this.validateSettings(newSettings);
        this.setState('settings', { ...this.state.settings, ...validatedSettings }, persist);
    }

    /**
     * Update MCP servers list
     * @param {Array} servers - New servers array
     */
    updateMcpServers(servers) {
        this.setState('mcpServers', Array.isArray(servers) ? servers : [], true);
    }

    /**
     * Update connection status
     * @param {boolean} isConnected - Connection status
     * @param {boolean} isConnecting - Connecting status
     */
    updateConnectionStatus(isConnected, isConnecting = false) {
        this.setState({
            isConnected: Boolean(isConnected),
            isConnecting: Boolean(isConnecting)
        });
    }

    /**
     * Add pending request
     * @param {number} requestId - Request ID
     * @param {Object} requestInfo - Request information
     */
    addPendingRequest(requestId, requestInfo) {
        this.state.pendingRequests.set(requestId, requestInfo);
    }

    /**
     * Remove pending request
     * @param {number} requestId - Request ID
     * @returns {Object|null} Removed request info
     */
    removePendingRequest(requestId) {
        const requestInfo = this.state.pendingRequests.get(requestId);
        this.state.pendingRequests.delete(requestId);
        return requestInfo || null;
    }

    /**
     * Get next request ID
     * @returns {number} Next request ID
     */
    getNextRequestId() {
        return ++this.state.requestId;
    }

    /**
     * Validate settings object
     * @private
     * @param {Object} settings - Settings to validate
     * @returns {Object} Validated settings
     */
    validateSettings(settings) {
        const validated = {};
        const defaults = this.getDefaultSettings();

        for (const [key, value] of Object.entries(settings)) {
            if (key in defaults) {
                // Type validation
                const expectedType = typeof defaults[key];
                if (typeof value === expectedType) {
                    validated[key] = value;
                } else {
                    console.warn(`[StateManager] Invalid type for setting ${key}, expected ${expectedType}`);
                }
            } else {
                console.warn(`[StateManager] Unknown setting: ${key}`);
            }
        }

        return validated;
    }

    /**
     * Validate entire state
     * @private
     */
    validateState() {
        // Ensure required properties exist
        if (!this.state.seamlessMode) {
            this.state.seamlessMode = this.getInitialState().seamlessMode;
        }

        if (!Array.isArray(this.state.mcpServers)) {
            this.state.mcpServers = [];
        }

        if (!(this.state.pendingRequests instanceof Map)) {
            this.state.pendingRequests = new Map();
        }
    }

    /**
     * Notify state change listeners
     * @private
     * @param {Object} oldState - Previous state
     * @param {Object} newState - New state
     */
    notifyListeners(oldState, newState) {
        // Notify global listeners
        const globalListeners = this.listeners.get('*');
        if (globalListeners) {
            globalListeners.forEach((callback) => {
                try {
                    callback(newState, oldState);
                } catch (error) {
                    console.error('[StateManager] Error in global listener:', error);
                }
            });
        }

        // Notify specific path listeners
        this.listeners.forEach((callbacks, path) => {
            if (path === '*') return; // Already handled above

            const oldValue = this.getNestedValue(oldState, path);
            const newValue = this.getNestedValue(newState, path);

            if (oldValue !== newValue) {
                callbacks.forEach((callback) => {
                    try {
                        callback(newValue, oldValue, path);
                    } catch (error) {
                        console.error(`[StateManager] Error in ${path} listener:`, error);
                    }
                });
            }
        });
    }

    /**
     * Get nested value from object using dot notation
     * @private
     * @param {Object} obj - Object to get value from
     * @param {string} path - Dot notation path
     * @returns {any} Value at path
     */
    getNestedValue(obj, path) {
        return path.split('.').reduce((current, key) => {
            return current && current[key] !== undefined ? current[key] : undefined;
        }, obj);
    }

    /**
     * Set nested value in object using dot notation
     * @private
     * @param {Object} obj - Object to set value in
     * @param {string} path - Dot notation path
     * @param {any} value - Value to set
     */
    setNestedValue(obj, path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        const target = keys.reduce((current, key) => {
            if (!current[key] || typeof current[key] !== 'object') {
                current[key] = {};
            }
            return current[key];
        }, obj);
        
        target[lastKey] = value;
    }

    /**
     * Load persisted state from storage
     * @private
     * @async
     * @returns {Promise<void>}
     */
    async loadPersistedState() {
        try {
            const result = await chrome.storage.sync.get(['mcpSettings']);
            if (result.mcpSettings) {
                this.setState('settings', { ...this.getDefaultSettings(), ...result.mcpSettings });
            }
        } catch (error) {
            console.warn('[StateManager] Failed to load persisted settings:', error);
        }
    }

    /**
     * Persist state to storage
     * @private
     * @param {string} path - State path that changed
     */
    async persistState(path) {
        if (typeof path === 'string' && path.startsWith('settings')) {
            try {
                await chrome.storage.sync.set({ mcpSettings: this.state.settings });
            } catch (error) {
                console.error('[StateManager] Failed to persist settings:', error);
            }
        }
    }

    /**
     * Reset state to initial values
     * @param {boolean} keepSettings - Whether to keep current settings
     */
    reset(keepSettings = true) {
        const currentSettings = keepSettings ? this.state.settings : this.getDefaultSettings();
        this.state = this.getInitialState();
        if (keepSettings) {
            this.state.settings = currentSettings;
        }
    }

    /**
     * Cleanup state manager
     */
    cleanup() {
        console.log('[StateManager] Starting cleanup...');
        
        try {
            this.listeners.clear();
            this.reset(false);
            this.isInitialized = false;
            console.log('[StateManager] Cleanup completed');
        } catch (error) {
            console.error('[StateManager] Error during cleanup:', error);
        }
    }

    /**
     * Get state statistics
     * @returns {Object} State statistics
     */
    getStats() {
        return {
            isInitialized: this.isInitialized,
            listenersCount: Array.from(this.listeners.values()).reduce((sum, map) => sum + map.size, 0),
            pendingRequestsCount: this.state.pendingRequests.size,
            mcpServersCount: this.state.mcpServers.length,
            isConnected: this.state.isConnected
        };
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = StateManager;
} else {
    window.StateManager = StateManager;
}
