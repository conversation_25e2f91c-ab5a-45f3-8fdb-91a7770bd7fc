/**
 * ObserverManager - Centralized mutation observer management
 * 
 * Manages all MutationObserver, ResizeObserver, and IntersectionObserver
 * instances with proper cleanup and performance optimization.
 * 
 * @version 2.0.0
 */

class ObserverManager {
    constructor() {
        this.observers = new Map();
        this.isInitialized = false;
        this.debounceTimers = new Map();
    }

    /**
     * Initialize the observer manager
     * @param {Object} client - Reference to the main MCP client
     */
    initialize(client) {
        this.client = client;
        this.isInitialized = true;
        console.log('[ObserverManager] Initialized successfully');
    }

    /**
     * Create a debounced mutation observer
     * @param {string} observerId - Unique identifier for the observer
     * @param {Function} callback - The callback function
     * @param {Object} options - Observer options
     * @param {number} debounceMs - Debounce delay in milliseconds
     * @returns {MutationObserver} The created observer
     */
    createMutationObserver(observerId, callback, options = {}, debounceMs = 0) {
        // Clean up existing observer with same ID
        this.disconnectObserver(observerId);

        const wrappedCallback = debounceMs > 0 
            ? this.debounce(callback, debounceMs)
            : callback;

        const observer = new MutationObserver((mutations, observer) => {
            try {
                wrappedCallback(mutations, observer);
            } catch (error) {
                console.error(`[ObserverManager] Error in ${observerId} callback:`, error);
            }
        });

        this.observers.set(observerId, {
            type: 'mutation',
            observer,
            callback,
            options,
            isConnected: false
        });

        return observer;
    }

    /**
     * Create a resize observer
     * @param {string} observerId - Unique identifier for the observer
     * @param {Function} callback - The callback function
     * @param {number} debounceMs - Debounce delay in milliseconds
     * @returns {ResizeObserver} The created observer
     */
    createResizeObserver(observerId, callback, debounceMs = 100) {
        // Clean up existing observer with same ID
        this.disconnectObserver(observerId);

        const wrappedCallback = debounceMs > 0 
            ? this.debounce(callback, debounceMs)
            : callback;

        const observer = new ResizeObserver((entries, observer) => {
            try {
                wrappedCallback(entries, observer);
            } catch (error) {
                console.error(`[ObserverManager] Error in ${observerId} callback:`, error);
            }
        });

        this.observers.set(observerId, {
            type: 'resize',
            observer,
            callback,
            isConnected: false
        });

        return observer;
    }

    /**
     * Create an intersection observer
     * @param {string} observerId - Unique identifier for the observer
     * @param {Function} callback - The callback function
     * @param {Object} options - Intersection observer options
     * @returns {IntersectionObserver} The created observer
     */
    createIntersectionObserver(observerId, callback, options = {}) {
        // Clean up existing observer with same ID
        this.disconnectObserver(observerId);

        const observer = new IntersectionObserver((entries, observer) => {
            try {
                callback(entries, observer);
            } catch (error) {
                console.error(`[ObserverManager] Error in ${observerId} callback:`, error);
            }
        }, options);

        this.observers.set(observerId, {
            type: 'intersection',
            observer,
            callback,
            options,
            isConnected: false
        });

        return observer;
    }

    /**
     * Start observing with a mutation observer
     * @param {string} observerId - The observer ID
     * @param {Element} target - The target element to observe
     * @param {Object} config - Mutation observer configuration
     */
    startMutationObserver(observerId, target, config = {}) {
        const observerInfo = this.observers.get(observerId);
        if (!observerInfo || observerInfo.type !== 'mutation') {
            console.error(`[ObserverManager] Mutation observer ${observerId} not found`);
            return;
        }

        const defaultConfig = {
            childList: true,
            subtree: true,
            attributes: false,
            attributeOldValue: false,
            characterData: false,
            characterDataOldValue: false
        };

        const finalConfig = { ...defaultConfig, ...config };

        try {
            observerInfo.observer.observe(target, finalConfig);
            observerInfo.isConnected = true;
            observerInfo.target = target;
            observerInfo.config = finalConfig;
            
            if (this.client?.settings?.debugLogging) {
                console.log(`[ObserverManager] Started mutation observer: ${observerId}`);
            }
        } catch (error) {
            console.error(`[ObserverManager] Failed to start mutation observer ${observerId}:`, error);
        }
    }

    /**
     * Start observing with a resize observer
     * @param {string} observerId - The observer ID
     * @param {Element} target - The target element to observe
     */
    startResizeObserver(observerId, target) {
        const observerInfo = this.observers.get(observerId);
        if (!observerInfo || observerInfo.type !== 'resize') {
            console.error(`[ObserverManager] Resize observer ${observerId} not found`);
            return;
        }

        try {
            observerInfo.observer.observe(target);
            observerInfo.isConnected = true;
            observerInfo.target = target;
            
            if (this.client?.settings?.debugLogging) {
                console.log(`[ObserverManager] Started resize observer: ${observerId}`);
            }
        } catch (error) {
            console.error(`[ObserverManager] Failed to start resize observer ${observerId}:`, error);
        }
    }

    /**
     * Start observing with an intersection observer
     * @param {string} observerId - The observer ID
     * @param {Element} target - The target element to observe
     */
    startIntersectionObserver(observerId, target) {
        const observerInfo = this.observers.get(observerId);
        if (!observerInfo || observerInfo.type !== 'intersection') {
            console.error(`[ObserverManager] Intersection observer ${observerId} not found`);
            return;
        }

        try {
            observerInfo.observer.observe(target);
            observerInfo.isConnected = true;
            observerInfo.target = target;
            
            if (this.client?.settings?.debugLogging) {
                console.log(`[ObserverManager] Started intersection observer: ${observerId}`);
            }
        } catch (error) {
            console.error(`[ObserverManager] Failed to start intersection observer ${observerId}:`, error);
        }
    }

    /**
     * Disconnect a specific observer
     * @param {string} observerId - The observer ID to disconnect
     */
    disconnectObserver(observerId) {
        const observerInfo = this.observers.get(observerId);
        if (observerInfo) {
            try {
                observerInfo.observer.disconnect();
                observerInfo.isConnected = false;
                
                if (this.client?.settings?.debugLogging) {
                    console.log(`[ObserverManager] Disconnected observer: ${observerId}`);
                }
            } catch (error) {
                console.error(`[ObserverManager] Error disconnecting observer ${observerId}:`, error);
            }
        }
    }

    /**
     * Remove an observer completely
     * @param {string} observerId - The observer ID to remove
     */
    removeObserver(observerId) {
        this.disconnectObserver(observerId);
        this.observers.delete(observerId);
        
        // Clear any associated debounce timers
        const timer = this.debounceTimers.get(observerId);
        if (timer) {
            clearTimeout(timer);
            this.debounceTimers.delete(observerId);
        }
    }

    /**
     * Debounce utility function
     * @param {Function} func - Function to debounce
     * @param {number} wait - Wait time in milliseconds
     * @returns {Function} Debounced function
     */
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func.apply(this, args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    /**
     * Throttle utility function
     * @param {Function} func - Function to throttle
     * @param {number} limit - Time limit in milliseconds
     * @returns {Function} Throttled function
     */
    throttle(func, limit) {
        let inThrottle;
        return function executedFunction(...args) {
            if (!inThrottle) {
                func.apply(this, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    }

    /**
     * Get observer information
     * @param {string} observerId - The observer ID
     * @returns {Object|null} Observer information
     */
    getObserverInfo(observerId) {
        return this.observers.get(observerId) || null;
    }

    /**
     * Check if an observer is connected
     * @param {string} observerId - The observer ID
     * @returns {boolean} True if connected
     */
    isObserverConnected(observerId) {
        const info = this.observers.get(observerId);
        return info ? info.isConnected : false;
    }

    /**
     * Get all observer IDs
     * @returns {string[]} Array of observer IDs
     */
    getObserverIds() {
        return Array.from(this.observers.keys());
    }

    /**
     * Cleanup all observers
     */
    cleanup() {
        console.log('[ObserverManager] Starting cleanup...');

        try {
            // Disconnect all observers
            this.observers.forEach((observerInfo, observerId) => {
                try {
                    observerInfo.observer.disconnect();
                } catch (error) {
                    console.warn(`[ObserverManager] Failed to disconnect ${observerId}:`, error);
                }
            });

            // Clear all timers
            this.debounceTimers.forEach((timer) => {
                clearTimeout(timer);
            });

            // Clear maps
            this.observers.clear();
            this.debounceTimers.clear();

            this.isInitialized = false;
            console.log('[ObserverManager] Cleanup completed');
        } catch (error) {
            console.error('[ObserverManager] Error during cleanup:', error);
        }
    }

    /**
     * Get statistics about observers
     * @returns {Object} Observer statistics
     */
    getStats() {
        const stats = {
            total: this.observers.size,
            connected: 0,
            byType: {
                mutation: 0,
                resize: 0,
                intersection: 0
            }
        };

        this.observers.forEach((info) => {
            if (info.isConnected) stats.connected++;
            stats.byType[info.type]++;
        });

        return stats;
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ObserverManager;
} else {
    window.ObserverManager = ObserverManager;
}
