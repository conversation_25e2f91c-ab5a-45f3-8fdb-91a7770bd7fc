/**
 * SeamlessMode - Manages seamless mode functionality
 * 
 * Handles dual textarea system, response monitoring, and seamless
 * tool execution without disrupting the user experience.
 * 
 * @version 2.0.0
 */

class SeamlessMode {
    constructor() {
        this.isInitialized = false;
        this.activeToolCalls = new Map();
        this.hiddenTextarea = null;
        this.userTextarea = null;
        this.responseElementCount = 0;
        this.lastPbLgCount = 0;
        this.pendingDeletions = [];
        this.completedWidgetStates = [];
        this.cleanedOriginalPrompts = [];
        this.deletedToolCallResults = [];
        this.resizeObserver = null;
        this.MAX_PROCESSING_ATTEMPTS = 3;
        this.PROCESSING_RETRY_DELAYS = [500, 1000, 1500];
    }

    /**
     * Initialize seamless mode
     * @param {Object} client - Reference to the main MCP client
     * @param {StateManager} stateManager - State manager instance
     * @param {ObserverManager} observerManager - Observer manager instance
     * @param {EventManager} eventManager - Event manager instance
     */
    initialize(client, stateManager, observerManager, eventManager) {
        this.client = client;
        this.stateManager = stateManager;
        this.observerManager = observerManager;
        this.eventManager = eventManager;
        
        this.isInitialized = true;
        console.log('[SeamlessMode] Initialized successfully');
    }

    /**
     * Set up seamless mode with proper async handling
     * @async
     * @returns {Promise<void>}
     */
    async setup() {
        console.log('[SeamlessMode] Setting up seamless mode...');

        try {
            // Only load thread state if we're in a valid thread URL
            if (this.client.isValidThreadUrl(window.location.href)) {
                this.loadThreadState();
            } else if (this.client.settings?.debugLogging) {
                console.log('[SeamlessMode] Not in a thread URL, skipping thread state loading');
            }

            // Set up dual textarea system with retry logic
            await this.setupDualTextareaSystemWithRetry();

            // Monitor for response element count changes
            this.startSeamlessResponseMonitoring();

            console.log('[SeamlessMode] Setup completed successfully');
        } catch (error) {
            console.error('[SeamlessMode] Failed to setup:', error);
            throw error;
        }
    }

    /**
     * Set up dual textarea system with retry logic
     * @async
     * @returns {Promise<void>}
     */
    async setupDualTextareaSystemWithRetry() {
        const maxRetries = 5;
        let retryCount = 0;

        while (retryCount < maxRetries) {
            const originalTextarea = document.querySelector('textarea#ask-input');
            if (originalTextarea) {
                this.setupDualTextareaSystem(originalTextarea);
                return;
            }

            retryCount++;
            console.log(`[SeamlessMode] Original textarea not found, retry ${retryCount}/${maxRetries}...`);
            await new Promise(resolve => setTimeout(resolve, 1000));
        }

        console.warn('[SeamlessMode] Failed to find original textarea after all retries');
    }

    /**
     * Set up dual textarea system for seamless mode
     * @param {HTMLTextAreaElement} originalTextarea - The original textarea element
     */
    setupDualTextareaSystem(originalTextarea) {
        // Remove existing overlay if it exists
        const existingOverlay = document.getElementById('ask-input-mcp-overlay');
        if (existingOverlay) {
            existingOverlay.remove();
        }

        // Hide the original textarea (but keep it functional for React)
        originalTextarea.style.opacity = '0';
        originalTextarea.style.pointerEvents = 'none';

        // Store reference to original (this will handle the enhanced prompts)
        this.hiddenTextarea = originalTextarea;
        this.stateManager?.setState('seamlessMode.hiddenTextarea', originalTextarea);

        // Create overlay textarea for user interaction
        this.userTextarea = originalTextarea.cloneNode(true);
        this.userTextarea.id = 'ask-input-mcp-overlay';
        this.userTextarea.value = ''; // Start clean

        // Position overlay exactly over the original using computed styles
        const computedStyle = getComputedStyle(originalTextarea);
        this.userTextarea.style.cssText = `
            position: absolute !important;
            top: ${originalTextarea.offsetTop}px !important;
            left: ${originalTextarea.offsetLeft}px !important;
            width: ${originalTextarea.offsetWidth}px !important;
            height: ${originalTextarea.offsetHeight}px !important;
            z-index: 10 !important;
            background: ${computedStyle.background} !important;
            border: ${computedStyle.border} !important;
            border-radius: ${computedStyle.borderRadius} !important;
            font-family: ${computedStyle.fontFamily} !important;
            font-size: ${computedStyle.fontSize} !important;
            padding: ${computedStyle.padding} !important;
            margin: 0 !important;
            resize: ${computedStyle.resize} !important;
            outline: none !important;
            box-sizing: border-box !important;
        `;

        // Copy important attributes
        this.userTextarea.placeholder = originalTextarea.placeholder;
        this.userTextarea.className = originalTextarea.className;

        // Insert overlay into the same parent
        originalTextarea.parentNode.insertBefore(this.userTextarea, originalTextarea);

        // Sync overlay changes back to original for React consistency
        this.setupTextareaSyncing();

        // Set up resize observer for responsive layout
        this.setupResizeObserver(originalTextarea);

        console.log('[SeamlessMode] Overlay textarea system setup complete');
    }

    /**
     * Set up textarea syncing between overlay and original
     * @private
     */
    setupTextareaSyncing() {
        const overlay = this.userTextarea;
        const original = this.hiddenTextarea;

        if (!overlay || !original) return;

        // Sync user input from overlay to original (without enhancement)
        const inputHandler = this.eventManager.addEventListener(
            overlay,
            'input',
            () => {
                // Keep original in sync with user input (for React state)
                this.client.sendTextInBackground(original, overlay.value);
            }
        );

        // Handle form submission from overlay
        const keydownHandler = this.eventManager.addEventListener(
            overlay,
            'keydown',
            (e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    const userPrompt = overlay.value.trim();
                    if (userPrompt) {
                        this.handleSeamlessSubmission(userPrompt);
                        overlay.value = ''; // Clear overlay after submission
                    }
                }
            }
        );

        // Store handler IDs for cleanup
        this.inputHandlerId = inputHandler;
        this.keydownHandlerId = keydownHandler;
    }

    /**
     * Set up resize observer for responsive layout
     * @private
     * @param {HTMLTextAreaElement} originalTextarea - Original textarea to observe
     */
    setupResizeObserver(originalTextarea) {
        if (!this.observerManager) return;

        this.observerManager.createResizeObserver(
            'seamlessTextareaResize',
            (entries) => {
                for (const entry of entries) {
                    if (entry.target === originalTextarea && this.userTextarea) {
                        // Update overlay position and size
                        const rect = originalTextarea.getBoundingClientRect();
                        this.userTextarea.style.top = `${originalTextarea.offsetTop}px`;
                        this.userTextarea.style.left = `${originalTextarea.offsetLeft}px`;
                        this.userTextarea.style.width = `${originalTextarea.offsetWidth}px`;
                        this.userTextarea.style.height = `${originalTextarea.offsetHeight}px`;
                    }
                }
            },
            100 // Debounce resize events
        );

        this.observerManager.startResizeObserver('seamlessTextareaResize', originalTextarea);
    }

    /**
     * Start seamless response monitoring
     */
    startSeamlessResponseMonitoring() {
        if (!this.observerManager) return;

        // Initialize count for seamless mode
        this.lastPbLgCount = document.querySelectorAll('.pb-lg').length;
        this.stateManager?.setState('seamlessMode.lastPbLgCount', this.lastPbLgCount);
        
        console.log('[SeamlessMode] Initial .pb-lg count:', this.lastPbLgCount);

        // Create observer for seamless response monitoring
        this.observerManager.createMutationObserver(
            'seamlessResponseMonitor',
            (mutations) => this.processSeamlessResponseMutations(mutations),
            {
                childList: true,
                subtree: true
            },
            200 // Debounce for seamless mode
        );

        this.observerManager.startMutationObserver('seamlessResponseMonitor', document.body);
        console.log('[SeamlessMode] Response monitoring started');
    }

    /**
     * Process mutations for seamless response monitoring
     * @private
     * @param {MutationRecord[]} mutations - Array of mutation records
     */
    processSeamlessResponseMutations(mutations) {
        try {
            if (!this.client.settings?.bridgeEnabled || !this.client.settings?.autoExecute) return;

            let potentiallyNewPbLg = false;
            
            for (const mutation of mutations) {
                if (mutation.type === 'childList') {
                    for (const node of mutation.addedNodes) {
                        if (node.nodeType === Node.ELEMENT_NODE) {
                            if (node.classList?.contains('pb-lg') || node.querySelector?.('.pb-lg')) {
                                potentiallyNewPbLg = true;
                                break;
                            }
                        }
                    }
                    if (potentiallyNewPbLg) break;
                }
            }
            
            if (potentiallyNewPbLg) {
                setTimeout(() => {
                    this.processNewestPbLgElement();
                    this.client.fixCopyQueryButtons?.();
                }, 200);
            }
        } catch (error) {
            console.error('[SeamlessMode] Error processing response mutations:', error);
        }
    }

    /**
     * Process the newest .pb-lg element
     */
    processNewestPbLgElement() {
        const currentPbLgElements = document.querySelectorAll('.pb-lg');
        const currentCount = currentPbLgElements.length;

        if (this.client.settings?.debugLogging) {
            console.log(`[SeamlessMode] processNewestPbLgElement. Current .pb-lg count: ${currentCount}, Last count: ${this.lastPbLgCount}`);
        }

        if (currentCount > this.lastPbLgCount) {
            const newElement = currentPbLgElements[currentCount - 1]; // Get the last one

            if (newElement && !newElement.dataset.mcpProcessingQueued) {
                if (this.client.settings?.debugLogging) {
                    console.log('[SeamlessMode] New .pb-lg element detected. Starting streaming content monitoring:', newElement);
                }
                newElement.dataset.mcpProcessingQueued = 'true';
                this.lastPbLgCount = currentCount; // Update count
                this.stateManager?.setState('seamlessMode.lastPbLgCount', currentCount);

                // Start continuous monitoring for this element
                this.client.startStreamingContentMonitor?.(newElement);
            }
        } else if (currentCount < this.lastPbLgCount) {
            if (this.client.settings?.debugLogging) {
                console.log('[SeamlessMode] .pb-lg count decreased. Resetting lastPbLgCount to current:', currentCount);
            }
            this.lastPbLgCount = currentCount;
            this.stateManager?.setState('seamlessMode.lastPbLgCount', currentCount);
        }
    }

    /**
     * Handle seamless submission
     * @param {string} userPrompt - User's prompt
     */
    handleSeamlessSubmission(userPrompt) {
        console.log('[SeamlessMode] Handling seamless submission for:', userPrompt);

        // Skip if tool result is in flight
        if (this.client.isSubmittingToolResult) {
            console.log('[SeamlessMode] Submission lock active, skipping seamless submission.');
            return;
        }

        // Do not re-submit MCP tool results as new prompts
        if (userPrompt.startsWith('[MCP Tool Result from')) {
            console.log('[SeamlessMode] Detected tool result in overlay, skipping submission.');
            return;
        }

        if (!this.hiddenTextarea || !this.userTextarea) {
            console.error('[SeamlessMode] Overlay textarea system not properly initialized');
            return;
        }

        // Update .pb-lg count before submission
        const currentPbLgCount = document.querySelectorAll('.pb-lg').length;
        this.lastPbLgCount = currentPbLgCount;
        this.stateManager?.setState('seamlessMode.lastPbLgCount', currentPbLgCount);
        console.log('[SeamlessMode] Before submission: Current .pb-lg count:', currentPbLgCount);

        // Check if we should enhance the prompt
        if (userPrompt.trim() && this.client.shouldEnhancePrompt?.(userPrompt)) {
            console.log('[SeamlessMode] Enhancing prompt in seamless mode');
            const systemPrompt = this.client.generateMcpSystemPrompt?.();
            if (systemPrompt) {
                const enhancedPrompt = `${userPrompt}${systemPrompt}`;

                // Use background text sending method for enhanced prompt
                console.log('[SeamlessMode] Setting enhanced prompt in original textarea');
                this.client.sendTextInBackground(this.hiddenTextarea, enhancedPrompt);

                // Store original user prompt for query cleanup
                this.client.lastUserPrompt = userPrompt;

                // Start real-time cleanup monitoring
                this.client.startRealtimeQueryCleanup?.(userPrompt);

                // Wait for React to process before submitting
                setTimeout(() => {
                    this.client.submitTextInBackground(this.hiddenTextarea);
                }, 200);
                return;
            }
        }

        // No enhancement needed - submit user prompt as-is
        console.log('[SeamlessMode] No enhancement needed, submitting user prompt as-is');
        this.client.submitTextInBackground(this.hiddenTextarea);
    }

    /**
     * Load thread state from storage
     */
    loadThreadState() {
        try {
            const threadId = this.client.extractThreadId?.(window.location.href);
            if (!threadId) return;

            // Load completed widget states
            const widgetKey = `mcp_completed_widgets_${threadId}`;
            const savedWidgets = localStorage.getItem(widgetKey);
            if (savedWidgets) {
                this.completedWidgetStates = JSON.parse(savedWidgets);
                this.stateManager?.setState('seamlessMode.loadedCompletedWidgetStates', this.completedWidgetStates);
            }

            // Load cleaned prompts
            const promptKey = `mcp_cleaned_prompts_${threadId}`;
            const savedPrompts = localStorage.getItem(promptKey);
            if (savedPrompts) {
                this.cleanedOriginalPrompts = JSON.parse(savedPrompts);
                this.stateManager?.setState('seamlessMode.loadedCleanedOriginalPrompts', this.cleanedOriginalPrompts);
            }

            // Load deleted tool call results
            const deletedKey = `mcp_deleted_results_${threadId}`;
            const savedDeleted = localStorage.getItem(deletedKey);
            if (savedDeleted) {
                this.deletedToolCallResults = JSON.parse(savedDeleted);
                this.stateManager?.setState('seamlessMode.loadedDeletedToolCallResults', this.deletedToolCallResults);
            }

            console.log('[SeamlessMode] Thread state loaded successfully');
        } catch (error) {
            console.error('[SeamlessMode] Failed to load thread state:', error);
        }
    }

    /**
     * Save thread state to storage
     */
    saveThreadState() {
        try {
            const threadId = this.client.extractThreadId?.(window.location.href);
            if (!threadId) return;

            // Save completed widget states
            const widgetKey = `mcp_completed_widgets_${threadId}`;
            localStorage.setItem(widgetKey, JSON.stringify(this.completedWidgetStates));

            // Save cleaned prompts
            const promptKey = `mcp_cleaned_prompts_${threadId}`;
            localStorage.setItem(promptKey, JSON.stringify(this.cleanedOriginalPrompts));

            // Save deleted tool call results
            const deletedKey = `mcp_deleted_results_${threadId}`;
            localStorage.setItem(deletedKey, JSON.stringify(this.deletedToolCallResults));

            console.log('[SeamlessMode] Thread state saved successfully');
        } catch (error) {
            console.error('[SeamlessMode] Failed to save thread state:', error);
        }
    }

    /**
     * Cleanup seamless mode
     */
    cleanup() {
        console.log('[SeamlessMode] Starting cleanup...');

        try {
            // Remove overlay textarea
            if (this.userTextarea && this.userTextarea.parentNode) {
                this.userTextarea.parentNode.removeChild(this.userTextarea);
            }

            // Restore original textarea
            if (this.hiddenTextarea) {
                this.hiddenTextarea.style.opacity = '';
                this.hiddenTextarea.style.pointerEvents = '';
            }

            // Remove event handlers
            if (this.inputHandlerId) {
                this.eventManager?.removeEventListener(this.inputHandlerId);
            }
            if (this.keydownHandlerId) {
                this.eventManager?.removeEventListener(this.keydownHandlerId);
            }

            // Disconnect observers
            this.observerManager?.disconnectObserver('seamlessResponseMonitor');
            this.observerManager?.disconnectObserver('seamlessTextareaResize');

            // Clear state
            this.activeToolCalls.clear();
            this.hiddenTextarea = null;
            this.userTextarea = null;
            this.isInitialized = false;

            console.log('[SeamlessMode] Cleanup completed');
        } catch (error) {
            console.error('[SeamlessMode] Error during cleanup:', error);
        }
    }

    /**
     * Get seamless mode statistics
     * @returns {Object} Statistics object
     */
    getStats() {
        return {
            isInitialized: this.isInitialized,
            activeToolCalls: this.activeToolCalls.size,
            lastPbLgCount: this.lastPbLgCount,
            completedWidgets: this.completedWidgetStates.length,
            cleanedPrompts: this.cleanedOriginalPrompts.length,
            deletedResults: this.deletedToolCallResults.length,
            hasTextareas: !!(this.hiddenTextarea && this.userTextarea)
        };
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = SeamlessMode;
} else {
    window.SeamlessMode = SeamlessMode;
}
