/**
 * ToolExecution - Manages MCP tool execution and widget creation
 * 
 * Handles tool parsing, execution, widget creation, and result processing
 * with proper error handling and state management.
 * 
 * @version 2.0.0
 */

class ToolExecution {
    constructor() {
        this.isInitialized = false;
        this.executionQueue = [];
        this.isProcessing = false;
        this.executionStats = {
            totalExecutions: 0,
            successfulExecutions: 0,
            failedExecutions: 0,
            averageExecutionTime: 0
        };
    }

    /**
     * Initialize tool execution manager
     * @param {Object} client - Reference to the main MCP client
     * @param {StateManager} stateManager - State manager instance
     * @param {ErrorHandler} errorHandler - Error handler instance
     */
    initialize(client, stateManager, errorHandler) {
        this.client = client;
        this.stateManager = stateManager;
        this.errorHandler = errorHandler;
        
        this.isInitialized = true;
        console.log('[ToolExecution] Initialized successfully');
    }

    /**
     * Parse and execute tool call from text content
     * @param {Element} element - DOM element containing the tool call
     * @param {string} textContent - Text content to parse
     * @returns {boolean} True if tool call was found and processed
     */
    parseAndExecuteToolCall(element, textContent) {
        try {
            if (!textContent || element.dataset.mcpToolCallHandled) {
                return false;
            }

            const toolCall = this.parseToolCallFromText(textContent);
            if (!toolCall) {
                return false;
            }

            // Mark element as handled to prevent duplicate processing
            element.dataset.mcpToolCallHandled = 'true';

            // Execute based on mode
            if (this.client.settings?.legacyMode) {
                this.executeLegacyToolCall(toolCall, element, textContent);
            } else {
                this.executeSeamlessToolCall(toolCall, element, textContent);
            }

            return true;
        } catch (error) {
            this.errorHandler?.handleError(error, 'parse_and_execute_tool_call', { element, textContent });
            return false;
        }
    }

    /**
     * Parse tool call from text content
     * @private
     * @param {string} textContent - Text content to parse
     * @returns {Object|null} Parsed tool call or null
     */
    parseToolCallFromText(textContent) {
        try {
            // Look for XML-style tool call pattern
            const toolCallMatch = textContent.match(/<mcp_tool\s+server="([^"]+)"\s+tool="([^"]+)"(?:\s+parameters="([^"]*)")?[^>]*>(.*?)<\/mcp_tool>/s);
            
            if (!toolCallMatch) {
                return null;
            }

            const [originalText, server, tool, parametersStr, content] = toolCallMatch;
            
            // Parse parameters
            let parameters = {};
            if (parametersStr) {
                try {
                    parameters = JSON.parse(parametersStr);
                } catch (parseError) {
                    console.warn('[ToolExecution] Failed to parse parameters:', parametersStr);
                }
            }

            // Extract parameters from content if present
            if (content && content.trim()) {
                try {
                    const contentParams = JSON.parse(content.trim());
                    parameters = { ...parameters, ...contentParams };
                } catch (parseError) {
                    // Content might not be JSON, treat as text parameter
                    if (content.trim()) {
                        parameters.content = content.trim();
                    }
                }
            }

            return {
                id: `tool_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
                server,
                tool,
                parameters,
                originalText,
                timestamp: Date.now()
            };
        } catch (error) {
            this.errorHandler?.handleError(error, 'parse_tool_call', { textContent });
            return null;
        }
    }

    /**
     * Execute tool call in legacy mode
     * @private
     * @param {Object} toolCall - Parsed tool call
     * @param {Element} element - DOM element
     * @param {string} textContent - Original text content
     */
    executeLegacyToolCall(toolCall, element, textContent) {
        try {
            console.log('[ToolExecution] Executing legacy tool call:', toolCall);
            
            // Create inline widget for legacy mode
            const widget = this.client.createInlineToolWidget?.(toolCall.originalText, element, toolCall);
            
            if (widget) {
                // Execute tool and update widget
                this.executeInlineToolCall(toolCall, widget);
            }
        } catch (error) {
            this.errorHandler?.handleError(error, 'execute_legacy_tool_call', { toolCall, element });
        }
    }

    /**
     * Execute tool call in seamless mode
     * @private
     * @param {Object} toolCall - Parsed tool call
     * @param {Element} element - DOM element
     * @param {string} textContent - Original text content
     */
    executeSeamlessToolCall(toolCall, element, textContent) {
        try {
            console.log('[ToolExecution] Executing seamless tool call:', toolCall);
            
            // Create animated widget for seamless mode
            const widget = this.createAnimatedToolWidget(toolCall);
            
            if (widget) {
                // Insert widget into appropriate location
                this.insertWidgetIntoElement(widget, element);
                
                // Execute tool with seamless workflow
                this.client.executeSeamlessToolCallWithWidget?.(toolCall, widget);
            }
        } catch (error) {
            this.errorHandler?.handleError(error, 'execute_seamless_tool_call', { toolCall, element });
        }
    }

    /**
     * Execute inline tool call with widget updates
     * @param {Object} toolCall - Tool call object
     * @param {Element} widget - Widget element
     * @returns {Promise<any>} Tool execution result
     */
    async executeInlineToolCall(toolCall, widget) {
        const startTime = Date.now();
        
        try {
            this.executionStats.totalExecutions++;
            
            // Update widget to show execution state
            this.setWidgetState(widget, 'executing', toolCall);
            
            // Execute the tool
            const result = await this.client.callMcpTool(toolCall.server, toolCall.tool, toolCall.parameters);
            
            // Update widget with success state
            this.setWidgetState(widget, 'completed', toolCall, result);
            
            // Update statistics
            this.executionStats.successfulExecutions++;
            this.updateAverageExecutionTime(Date.now() - startTime);
            
            console.log('[ToolExecution] Tool execution completed successfully:', toolCall.tool);
            return result;
            
        } catch (error) {
            // Update widget with error state
            this.setWidgetState(widget, 'error', toolCall, null, error);
            
            // Update statistics
            this.executionStats.failedExecutions++;
            this.updateAverageExecutionTime(Date.now() - startTime);
            
            this.errorHandler?.handleError(error, 'execute_inline_tool_call', { toolCall });
            throw error;
        }
    }

    /**
     * Create animated tool widget
     * @param {Object} toolCall - Tool call object
     * @returns {Element} Widget element
     */
    createAnimatedToolWidget(toolCall) {
        try {
            const widget = document.createElement('div');
            widget.className = 'mcp-tool-widget';
            widget.dataset.toolId = toolCall.id;
            widget.dataset.server = toolCall.server;
            widget.dataset.tool = toolCall.tool;
            
            // Set initial content
            widget.innerHTML = this.getWidgetHTML(toolCall, 'pending');
            
            // Add CSS for animations
            widget.style.cssText = `
                border: 1px solid #e5e7eb;
                border-radius: 8px;
                padding: 12px;
                margin: 8px 0;
                background: #f9fafb;
                font-family: system-ui, sans-serif;
                font-size: 14px;
                transition: all 0.3s ease;
                position: relative;
                overflow: hidden;
            `;
            
            return widget;
        } catch (error) {
            this.errorHandler?.handleError(error, 'create_animated_tool_widget', { toolCall });
            return null;
        }
    }

    /**
     * Get widget HTML for different states
     * @private
     * @param {Object} toolCall - Tool call object
     * @param {string} state - Widget state
     * @param {any} data - Additional data (result or error)
     * @returns {string} HTML string
     */
    getWidgetHTML(toolCall, state, data = null) {
        const serverName = this.getServerDisplayName(toolCall.server);
        const toolName = toolCall.tool;
        
        switch (state) {
            case 'pending':
                return `
                    <div class="mcp-widget-header">
                        <span class="mcp-widget-icon">⏳</span>
                        <span class="mcp-widget-title">Preparing ${toolName}</span>
                        <span class="mcp-widget-server">${serverName}</span>
                    </div>
                    <div class="mcp-widget-content">
                        <div class="mcp-loading">Initializing tool execution...</div>
                    </div>
                `;
                
            case 'executing':
                return `
                    <div class="mcp-widget-header">
                        <span class="mcp-widget-icon mcp-spinning">⚙️</span>
                        <span class="mcp-widget-title">Executing ${toolName}</span>
                        <span class="mcp-widget-server">${serverName}</span>
                    </div>
                    <div class="mcp-widget-content">
                        <div class="mcp-loading">Tool execution in progress...</div>
                        <div class="mcp-progress-bar">
                            <div class="mcp-progress-fill"></div>
                        </div>
                    </div>
                `;
                
            case 'completed':
                return `
                    <div class="mcp-widget-header">
                        <span class="mcp-widget-icon">✅</span>
                        <span class="mcp-widget-title">${toolName} completed</span>
                        <span class="mcp-widget-server">${serverName}</span>
                    </div>
                    <div class="mcp-widget-content">
                        <div class="mcp-result">${this.formatToolResult(data)}</div>
                    </div>
                `;
                
            case 'error':
                return `
                    <div class="mcp-widget-header">
                        <span class="mcp-widget-icon">❌</span>
                        <span class="mcp-widget-title">${toolName} failed</span>
                        <span class="mcp-widget-server">${serverName}</span>
                    </div>
                    <div class="mcp-widget-content">
                        <div class="mcp-error">Error: ${data?.message || 'Unknown error'}</div>
                    </div>
                `;
                
            default:
                return `<div class="mcp-widget-content">Unknown state: ${state}</div>`;
        }
    }

    /**
     * Set widget state with animation
     * @param {Element} widget - Widget element
     * @param {string} state - New state
     * @param {Object} toolCall - Tool call object
     * @param {any} result - Tool result (for completed state)
     * @param {Error} error - Error object (for error state)
     */
    setWidgetState(widget, state, toolCall, result = null, error = null) {
        try {
            if (!widget) return;
            
            // Add state class
            widget.className = `mcp-tool-widget mcp-state-${state}`;
            
            // Update content
            const data = result || error;
            widget.innerHTML = this.getWidgetHTML(toolCall, state, data);
            
            // Add state-specific styling
            this.applyStateStyles(widget, state);
            
            // Trigger animation
            widget.style.transform = 'scale(1.02)';
            setTimeout(() => {
                widget.style.transform = 'scale(1)';
            }, 150);
            
        } catch (error) {
            this.errorHandler?.handleError(error, 'set_widget_state', { widget, state, toolCall });
        }
    }

    /**
     * Apply state-specific styles to widget
     * @private
     * @param {Element} widget - Widget element
     * @param {string} state - Widget state
     */
    applyStateStyles(widget, state) {
        const stateStyles = {
            pending: { borderColor: '#d1d5db', background: '#f9fafb' },
            executing: { borderColor: '#3b82f6', background: '#eff6ff' },
            completed: { borderColor: '#10b981', background: '#ecfdf5' },
            error: { borderColor: '#ef4444', background: '#fef2f2' }
        };
        
        const styles = stateStyles[state] || stateStyles.pending;
        Object.assign(widget.style, styles);
    }

    /**
     * Format tool result for display
     * @param {any} result - Tool execution result
     * @returns {string} Formatted result
     */
    formatToolResult(result) {
        try {
            if (!result) return 'No result';
            
            if (typeof result === 'string') {
                return this.escapeHtml(result);
            }
            
            if (typeof result === 'object') {
                // Handle common result formats
                if (result.content) {
                    return this.escapeHtml(String(result.content));
                }
                
                if (result.text) {
                    return this.escapeHtml(String(result.text));
                }
                
                if (result.message) {
                    return this.escapeHtml(String(result.message));
                }
                
                // Fallback to JSON representation
                return `<pre>${this.escapeHtml(JSON.stringify(result, null, 2))}</pre>`;
            }
            
            return this.escapeHtml(String(result));
        } catch (error) {
            return 'Error formatting result';
        }
    }

    /**
     * Get server display name
     * @private
     * @param {string} serverId - Server ID
     * @returns {string} Display name
     */
    getServerDisplayName(serverId) {
        const servers = this.stateManager?.getState('mcpServers') || [];
        const server = servers.find(s => s.id === serverId);
        return server?.name || serverId;
    }

    /**
     * Insert widget into DOM element
     * @private
     * @param {Element} widget - Widget to insert
     * @param {Element} targetElement - Target element
     */
    insertWidgetIntoElement(widget, targetElement) {
        try {
            // Find the best insertion point
            const proseElement = targetElement.querySelector('.prose');
            const insertionTarget = proseElement || targetElement;
            
            // Insert widget
            insertionTarget.appendChild(widget);
            
            console.log('[ToolExecution] Widget inserted into element');
        } catch (error) {
            this.errorHandler?.handleError(error, 'insert_widget', { widget, targetElement });
        }
    }

    /**
     * Update average execution time
     * @private
     * @param {number} executionTime - Execution time in milliseconds
     */
    updateAverageExecutionTime(executionTime) {
        const total = this.executionStats.totalExecutions;
        const current = this.executionStats.averageExecutionTime;
        this.executionStats.averageExecutionTime = ((current * (total - 1)) + executionTime) / total;
    }

    /**
     * Escape HTML to prevent XSS
     * @private
     * @param {string} text - Text to escape
     * @returns {string} Escaped text
     */
    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * Queue tool execution
     * @param {Object} toolCall - Tool call to queue
     * @param {Element} widget - Associated widget
     */
    queueExecution(toolCall, widget) {
        this.executionQueue.push({ toolCall, widget, timestamp: Date.now() });
        this.processExecutionQueue();
    }

    /**
     * Process execution queue
     * @private
     */
    async processExecutionQueue() {
        if (this.isProcessing || this.executionQueue.length === 0) {
            return;
        }
        
        this.isProcessing = true;
        
        try {
            while (this.executionQueue.length > 0) {
                const { toolCall, widget } = this.executionQueue.shift();
                await this.executeInlineToolCall(toolCall, widget);
                
                // Small delay between executions to prevent overwhelming
                await new Promise(resolve => setTimeout(resolve, 100));
            }
        } catch (error) {
            this.errorHandler?.handleError(error, 'process_execution_queue');
        } finally {
            this.isProcessing = false;
        }
    }

    /**
     * Get execution statistics
     * @returns {Object} Execution statistics
     */
    getStats() {
        return {
            ...this.executionStats,
            queueLength: this.executionQueue.length,
            isProcessing: this.isProcessing,
            isInitialized: this.isInitialized
        };
    }

    /**
     * Cleanup tool execution manager
     */
    cleanup() {
        console.log('[ToolExecution] Starting cleanup...');
        
        try {
            this.executionQueue.length = 0;
            this.isProcessing = false;
            this.isInitialized = false;
            
            console.log('[ToolExecution] Cleanup completed');
        } catch (error) {
            console.error('[ToolExecution] Error during cleanup:', error);
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ToolExecution;
} else {
    window.ToolExecution = ToolExecution;
}
