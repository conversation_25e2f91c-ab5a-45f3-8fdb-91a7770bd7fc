/**
 * EventManager - Centralized event handling for MCP Bridge
 * 
 * Manages all event listeners, message handlers, and provides
 * proper cleanup and memory management for events.
 * 
 * @version 2.0.0
 */

class EventManager {
    constructor() {
        this.eventHandlers = new Map();
        this.messageHandlers = new Map();
        this.storageHandlers = new Map();
        this.isInitialized = false;
    }

    /**
     * Initialize the event manager
     * @param {Object} client - Reference to the main MCP client
     */
    initialize(client) {
        this.client = client;
        this.setupMessageListener();
        this.setupStorageListener();
        this.isInitialized = true;
        console.log('[EventManager] Initialized successfully');
    }

    /**
     * Set up Chrome runtime message listener with error handling
     * @private
     */
    setupMessageListener() {
        const messageHandler = (message, sender, sendResponse) => {
            try {
                this.handleBackgroundMessage(message, sender, sendResponse);
                return true; // Keep message channel open for async responses
            } catch (error) {
                console.error('[EventManager] Message handler error:', error);
                if (sendResponse) {
                    sendResponse({ error: error.message });
                }
                return false;
            }
        };

        chrome.runtime.onMessage.addListener(messageHandler);
        this.eventHandlers.set('messageListener', messageHandler);
    }

    /**
     * Set up Chrome storage change listener
     * @private
     */
    setupStorageListener() {
        const storageHandler = (changes, namespace) => {
            try {
                this.handleStorageChange(changes, namespace);
            } catch (error) {
                console.error('[EventManager] Storage change handler error:', error);
            }
        };

        chrome.storage.onChanged.addListener(storageHandler);
        this.eventHandlers.set('storageListener', storageHandler);
    }

    /**
     * Handle messages from background script
     * @param {Object} message - The message from background script
     * @param {Object} sender - Message sender
     * @param {Function} sendResponse - Response callback
     */
    handleBackgroundMessage(message, sender, sendResponse) {
        if (!this.client || !this.client.settings.debugLogging) {
            // Only log if debug logging is enabled and client exists
        } else {
            console.log('[EventManager] Received message from background:', message);
        }

        const handler = this.messageHandlers.get(message.type);
        if (handler) {
            handler(message, sender, sendResponse);
        } else {
            this.handleUnknownMessage(message, sender, sendResponse);
        }
    }

    /**
     * Handle storage changes
     * @param {Object} changes - Storage changes
     * @param {string} namespace - Storage namespace
     */
    handleStorageChange(changes, namespace) {
        if (namespace === 'sync' && changes.mcpSettings) {
            const handler = this.storageHandlers.get('mcpSettings');
            if (handler) {
                handler(changes.mcpSettings);
            }
        }
    }

    /**
     * Register a message handler for a specific message type
     * @param {string} messageType - The message type to handle
     * @param {Function} handler - The handler function
     */
    registerMessageHandler(messageType, handler) {
        this.messageHandlers.set(messageType, handler);
    }

    /**
     * Register a storage change handler
     * @param {string} key - The storage key to watch
     * @param {Function} handler - The handler function
     */
    registerStorageHandler(key, handler) {
        this.storageHandlers.set(key, handler);
    }

    /**
     * Remove a message handler
     * @param {string} messageType - The message type to remove
     */
    unregisterMessageHandler(messageType) {
        this.messageHandlers.delete(messageType);
    }

    /**
     * Remove a storage handler
     * @param {string} key - The storage key to remove
     */
    unregisterStorageHandler(key) {
        this.storageHandlers.delete(key);
    }

    /**
     * Handle unknown message types
     * @private
     * @param {Object} message - The unknown message
     * @param {Object} sender - Message sender
     * @param {Function} sendResponse - Response callback
     */
    handleUnknownMessage(message, sender, sendResponse) {
        if (this.client?.settings?.debugLogging) {
            console.log('[EventManager] Unknown message type:', message.type);
        }
    }

    /**
     * Add a DOM event listener with automatic cleanup tracking
     * @param {Element} element - The element to add listener to
     * @param {string} event - The event type
     * @param {Function} handler - The event handler
     * @param {Object} options - Event listener options
     * @returns {string} Handler ID for removal
     */
    addEventListener(element, event, handler, options = {}) {
        const handlerId = `${event}_${Date.now()}_${Math.random()}`;
        
        const wrappedHandler = (e) => {
            try {
                handler(e);
            } catch (error) {
                console.error(`[EventManager] Error in ${event} handler:`, error);
            }
        };

        element.addEventListener(event, wrappedHandler, options);
        
        this.eventHandlers.set(handlerId, {
            element,
            event,
            handler: wrappedHandler,
            options
        });

        return handlerId;
    }

    /**
     * Remove a specific event listener
     * @param {string} handlerId - The handler ID returned by addEventListener
     */
    removeEventListener(handlerId) {
        const handlerInfo = this.eventHandlers.get(handlerId);
        if (handlerInfo) {
            handlerInfo.element.removeEventListener(
                handlerInfo.event,
                handlerInfo.handler,
                handlerInfo.options
            );
            this.eventHandlers.delete(handlerId);
        }
    }

    /**
     * Send message to background script with proper error handling
     * @param {Object} message - The message to send
     * @returns {Promise<any>} Promise resolving to the response
     */
    async sendMessageToBackground(message) {
        return new Promise((resolve, reject) => {
            try {
                chrome.runtime.sendMessage(message, (response) => {
                    if (chrome.runtime.lastError) {
                        reject(new Error(chrome.runtime.lastError.message));
                    } else {
                        resolve(response);
                    }
                });
            } catch (error) {
                reject(error);
            }
        });
    }

    /**
     * Cleanup all event listeners and handlers
     */
    cleanup() {
        console.log('[EventManager] Starting cleanup...');

        try {
            // Remove Chrome API listeners
            this.eventHandlers.forEach((handler, key) => {
                try {
                    if (key === 'messageListener') {
                        chrome.runtime.onMessage.removeListener(handler);
                    } else if (key === 'storageListener') {
                        chrome.storage.onChanged.removeListener(handler);
                    } else if (handler.element && handler.event) {
                        // DOM event listener
                        handler.element.removeEventListener(
                            handler.event,
                            handler.handler,
                            handler.options
                        );
                    }
                } catch (error) {
                    console.warn(`[EventManager] Failed to remove ${key}:`, error);
                }
            });

            // Clear all handler maps
            this.eventHandlers.clear();
            this.messageHandlers.clear();
            this.storageHandlers.clear();

            this.isInitialized = false;
            console.log('[EventManager] Cleanup completed');
        } catch (error) {
            console.error('[EventManager] Error during cleanup:', error);
        }
    }

    /**
     * Get statistics about registered handlers
     * @returns {Object} Handler statistics
     */
    getStats() {
        return {
            eventHandlers: this.eventHandlers.size,
            messageHandlers: this.messageHandlers.size,
            storageHandlers: this.storageHandlers.size,
            isInitialized: this.isInitialized
        };
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = EventManager;
} else {
    window.EventManager = EventManager;
}
