<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MCP Tool Widget Styles Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Robot<PERSON>, sans-serif;
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
            background: #0a0a0a;
            min-height: 100vh;
            line-height: 1.6;
        }
        
        h1 {
            text-align: center;
            color: white;
            margin-bottom: 30px;
            font-weight: 600;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }
        
        h2 {
            color: white;
            margin-top: 40px;
            margin-bottom: 15px;
            border-bottom: 2px solid rgba(255,255,255,0.3);
            padding-bottom: 5px;
            font-weight: 500;
        }
        
        .widget-container {
            background: #1a1a1a;
            border: 1px solid #2a2a2a;
            padding: 20px;
            border-radius: 12px;
            margin-bottom: 20px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        
        .description {
            margin-bottom: 15px;
            padding: 12px;
            background: rgba(32, 178, 170, 0.1);
            border-radius: 8px;
            color: #20b2aa;
            border-left: 4px solid #20b2aa;
        }
        
        /* Modern animation styles */
        @keyframes mcpPulse {
            0%, 100% { 
                opacity: 0.95; 
                transform: scale(1); 
            }
            50% { 
                opacity: 1; 
                transform: scale(1.01); 
            }
        }
        @keyframes mcpSlideIn {
            0% { 
                opacity: 0; 
                transform: translateY(-15px) scale(0.98); 
            }
            100% { 
                opacity: 1; 
                transform: translateY(0) scale(1); 
            }
        }
        @keyframes mcpShake {
            0%, 100% { transform: translateX(0); }
            10%, 30%, 50%, 70%, 90% { transform: translateX(-3px); }
            20%, 40%, 60%, 80% { transform: translateX(3px); }
        }
        @keyframes mcpSpinDots {
            0%, 80%, 100% { 
                transform: scale(0) rotate(0deg); 
                opacity: 0.3; 
            }
            40% { 
                transform: scale(1.2) rotate(180deg); 
                opacity: 1; 
            }
        }
        @keyframes mcpGlow {
            0%, 100% { box-shadow: 0 0 20px rgba(103, 58, 183, 0.3); }
            50% { box-shadow: 0 0 30px rgba(103, 58, 183, 0.6); }
        }
        
        .mcp-loading-dots {
            display: inline-flex;
            align-items: center;
            gap: 6px;
            margin-right: 12px;
        }
        .mcp-loading-dots div {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: currentColor;
            animation: mcpSpinDots 1.4s linear infinite;
        }
        .mcp-loading-dots div:nth-child(1) { animation-delay: -0.32s; }
        .mcp-loading-dots div:nth-child(2) { animation-delay: -0.16s; }
        .mcp-loading-dots div:nth-child(3) { animation-delay: 0s; }
        
        /* Modern widget base styles */
        .mcp-inline-tool-widget {
            display: block;
            width: 100%;
            margin: 0;
            padding: 20px;
            border-radius: 12px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            position: relative;
            transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }
        
        .mcp-inline-tool-widget::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: currentColor;
            opacity: 0.8;
        }
        
        .mcp-widget-header {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 12px;
            font-weight: 600;
        }
        
        .mcp-widget-title {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .mcp-widget-icon {
            font-size: 18px;
            animation: mcpPulse 2s ease-in-out infinite;
        }
        
        .mcp-close-btn {
            background: rgba(0,0,0,0.1);
            color: currentColor;
            border: none;
            border-radius: 6px;
            padding: 4px 8px;
            cursor: pointer;
            font-size: 12px;
            transition: all 0.2s ease;
            opacity: 0.7;
        }
        
        .mcp-close-btn:hover {
            opacity: 1;
            background: rgba(0,0,0,0.2);
            transform: scale(1.1);
        }
        
        .mcp-status-section {
            background: rgba(255,255,255,0.1);
            padding: 12px;
            border-radius: 8px;
            margin-bottom: 12px;
            backdrop-filter: blur(10px);
        }
        
        .mcp-status-row {
            display: flex;
            justify-content: space-between;
            margin-bottom: 4px;
        }
        
        .mcp-status-row:last-child {
            margin-bottom: 0;
        }
        
        .mcp-details {
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            overflow: hidden;
        }
        
        .mcp-details summary {
            font-weight: 600;
            padding: 8px 0;
            list-style: none;
            position: relative;
            cursor: pointer;
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        .mcp-details summary::-webkit-details-marker {
            display: none;
        }
        
        .mcp-details summary::after {
            content: ' ▼';
            position: static;
            transition: transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
            font-size: 12px;
            margin-left: 6px;
            display: inline-block;
        }
        
        .mcp-details summary:hover::after {
            transform: scale(1.2);
        }
        
        .mcp-details[open] summary::after {
            transform: rotate(180deg);
        }
        
        .mcp-details[open] summary:hover::after {
            transform: rotate(180deg) scale(1.2);
        }
        
        .mcp-result-content {
            margin-top: 12px;
            padding: 12px;
            background: rgba(255,255,255,0.1);
            border-radius: 8px;
            font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
            font-size: 12px;
            line-height: 1.5;
            max-height: 300px;
            overflow-y: auto;
            white-space: pre-wrap;
            backdrop-filter: blur(5px);
            animation: mcpExpandContent 0.4s cubic-bezier(0.4, 0, 0.2, 1) forwards;
        }
        
        @keyframes mcpExpandContent {
            0% {
                opacity: 0;
                transform: translateY(-10px);
                max-height: 0;
            }
            100% {
                opacity: 1;
                transform: translateY(0);
                max-height: 300px;
            }
        }
        
        /* Loading state - Extension theme */
        .loading {
            background: linear-gradient(135deg, #20b2aa 0%, #1a9a92 100%);
            color: white;
            animation: mcpPulse 3s ease-in-out infinite;
        }
        
        .loading .mcp-widget-icon {
            animation: mcpSpinDots 1.4s linear infinite;
        }
        
        /* Success state - Extension theme */
        .success {
            background: linear-gradient(180deg, #28a745 0%, #20b2aa 100%);
            color: white;
            animation: mcpSlideIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
        }
        
        /* Error state - Extension theme */
        .error {
            background: linear-gradient(180deg, #dc3545 0%, #c82333 100%);
            color: white;
            animation: mcpShake 0.6s ease-in-out;
        }
        
        .demo-button {
            background: linear-gradient(135deg, #20b2aa, #1a9a92);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            margin: 8px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(32, 178, 170, 0.3);
        }
        
        .demo-button:hover {
            background: linear-gradient(135deg, #1a9a92, #158a82);
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(32, 178, 170, 0.4);
        }
        
        .controls {
            text-align: center;
            margin: 20px 0;
            padding: 20px;
            background: #1a1a1a;
            border: 1px solid #2a2a2a;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.3);
        }
        
        .controls h3 {
            color: white;
            margin-bottom: 10px;
        }
        
        .controls p {
            color: rgba(255,255,255,0.9);
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <h1>🚀 Modern MCP Tool Widget Styles</h1>
    
    <div class="controls">
        <h3>Interactive Demo</h3>
        <p>Click the buttons below to see the different widget states:</p>
        <button class="demo-button" onclick="showLoading()">🔄 Show Loading</button>
        <button class="demo-button" onclick="showSuccess()">✅ Show Success</button>
        <button class="demo-button" onclick="showError()">❌ Show Error</button>
        <button class="demo-button" onclick="showAll()">📋 Show All States</button>
    </div>

    <div id="demo-area">
        <!-- Widgets will be inserted here -->
    </div>

    <h2>1. 🔄 Loading State</h2>
    <div class="widget-container">
        <div class="description">
            <strong>Loading State:</strong> Modern design with animated dots, smooth pulse effect, and expandable request details.
        </div>
        <div class="mcp-inline-tool-widget loading">
            <div class="mcp-widget-header">
                <div class="mcp-widget-title">
                    <div class="mcp-loading-dots">
                        <div></div><div></div><div></div>
                    </div>
                    <span>Executing MCP Tool: context7/resolve-library-id</span>
                </div>
                <button class="mcp-close-btn" onclick="this.closest('.mcp-inline-tool-widget').style.display='none'">✖</button>
            </div>
            <div class="mcp-status-section">
                <div class="mcp-status-row">
                    <strong>Status:</strong>
                    <span>⏳ Executing...</span>
                </div>
                <div class="mcp-status-row">
                    <strong>Started:</strong>
                    <span>10:05:30 AM</span>
                </div>
                <div class="mcp-status-row">
                    <strong>Duration:</strong>
                    <span class="mcp-stopwatch">0.0s</span>
                </div>
            </div>
            <details class="mcp-details">
                <summary>Request Details</summary>
                <div class="mcp-result-content">{
  "serverId": "context7",
  "toolName": "resolve-library-id", 
  "parameters": {
    "libraryName": "vue"
  }
}</div>
            </details>
        </div>
    </div>

    <h2>2. ✅ Success State</h2>
    <div class="widget-container">
        <div class="description">
            <strong>Success State:</strong> Clean success indication with expandable result details and modern styling.
        </div>
        <div class="mcp-inline-tool-widget success">
            <div class="mcp-widget-header">
                <div class="mcp-widget-title">
                    <span class="mcp-widget-icon">✅</span>
                    <span>MCP Tool Result: context7/resolve-library-id</span>
                </div>
                <button class="mcp-close-btn" onclick="this.closest('.mcp-inline-tool-widget').style.display='none'">✖</button>
            </div>
            <div class="mcp-status-section">
                <div class="mcp-status-row">
                    <strong>Status:</strong> 
                    <span>✅ Success</span>
                </div>
                <div class="mcp-status-row">
                    <strong>Executed:</strong> 
                    <span>10:05:35 AM</span>
                </div>
                <div class="mcp-status-row">
                    <strong>Duration:</strong> 
                    <span>1.2s</span>
                </div>
            </div>
            <details class="mcp-details" open>
                <summary>Result (3,817 characters)</summary>
                <div class="mcp-result-content">{
  "type": "text",
  "text": "Available Libraries (top matches):\n\nEach result includes:\n- Library ID: Context7-compatible identifier (format: /org/project)\n- Name: Library or package name\n- Description: Short summary\n- Code Snippets: Number of available code examples\n- Trust Score: Authority indicator\n- Versions: List of versions if available.\n\n----------\n\n- Title: Vue.js\n- Context7-compatible library ID: /vuejs/core\n- Description: 🖖 Vue.js is a progressive, incrementally-adoptable JavaScript framework for building UI on the web.\n- Code Snippets: 35\n- Trust Score: 9.7\n----------\n- Title: Vue.js Router\n- Context7-compatible library ID: /vuejs/router\n- Description: 🚦 The official router for Vue.js\n- Code Snippets: 260\n- Trust Score: 9.7"
}</div>
            </details>
        </div>
    </div>

    <h2>3. ❌ Error State</h2>
    <div class="widget-container">
        <div class="description">
            <strong>Error State:</strong> Clear error indication with detailed error information and modern styling.
        </div>
        <div class="mcp-inline-tool-widget error">
            <div class="mcp-widget-header">
                <div class="mcp-widget-title">
                    <span class="mcp-widget-icon">❌</span>
                    <span>MCP Tool Error: context7/resolve-library-id</span>
                </div>
                <button class="mcp-close-btn" onclick="this.closest('.mcp-inline-tool-widget').style.display='none'">✖</button>
            </div>
            <div class="mcp-status-section">
                <div class="mcp-status-row">
                    <strong>Status:</strong> 
                    <span>❌ Failed</span>
                </div>
                <div class="mcp-status-row">
                    <strong>Time:</strong> 
                    <span>10:05:40 AM</span>
                </div>
                <div class="mcp-status-row">
                    <strong>Duration:</strong> 
                    <span>0.8s</span>
                </div>
            </div>
            <details class="mcp-details">
                <summary>Error Details</summary>
                <div class="mcp-result-content">Error: Server context7 is not connected to MCP bridge

Stack trace:
  at mcpClient.executeToolInContext (content.js:1395)
  at mcpClient.executeInlineToolCall (content.js:1401)
  at timeout (content.js:1407)</div>
            </details>
        </div>
    </div>

    <script>
        function showLoading() {
            const area = document.getElementById('demo-area');
            area.innerHTML = `
                <div class="widget-container">
                    <h3>🔄 Loading Demo</h3>
                    <div class="mcp-inline-tool-widget loading">
                        <div class="mcp-widget-header">
                            <div class="mcp-widget-title">
                                <div class="mcp-loading-dots">
                                    <div></div><div></div><div></div>
                                </div>
                                <span>Executing MCP Tool: filesystem/list-files</span>
                            </div>
                            <button class="mcp-close-btn" onclick="this.closest('.widget-container').remove()">✖</button>
                        </div>
                        <div class="mcp-status-section">
                            <div class="mcp-status-row">
                                <strong>Status:</strong> 
                                <span>⏳ Executing...</span>
                            </div>
                            <div class="mcp-status-row">
                                <strong>Started:</strong> 
                                <span>${new Date().toLocaleTimeString()}</span>
                            </div>
                        </div>
                        <details class="mcp-details">
                            <summary>Request Details</summary>
                            <div class="mcp-result-content">{
  "serverId": "filesystem",
  "toolName": "list-files",
  "parameters": {
    "path": "/home/<USER>/projects"
  }
}</div>
                        </details>
                    </div>
                </div>
            `;
        }

        function showSuccess() {
            const area = document.getElementById('demo-area');
            area.innerHTML = `
                <div class="widget-container">
                    <h3>✅ Success Demo</h3>
                    <div class="mcp-inline-tool-widget success">
                        <div class="mcp-widget-header">
                            <div class="mcp-widget-title">
                                <span class="mcp-widget-icon">✅</span>
                                <span>MCP Tool Result: filesystem/list-files</span>
                            </div>
                            <button class="mcp-close-btn" onclick="this.closest('.widget-container').remove()">✖</button>
                        </div>
                        <div class="mcp-status-section">
                            <div class="mcp-status-row">
                                <strong>Status:</strong> 
                                <span>✅ Success</span>
                            </div>
                            <div class="mcp-status-row">
                                <strong>Executed:</strong> 
                                <span>${new Date().toLocaleTimeString()}</span>
                            </div>
                            <div class="mcp-status-row">
                                <strong>Duration:</strong> 
                                <span>0.4s</span>
                            </div>
                        </div>
                        <details class="mcp-details">
                            <summary>Result (245 characters)</summary>
                            <div class="mcp-result-content">["package.json", "README.md", "src/", "dist/", "node_modules/", ".git/", ".gitignore", "tsconfig.json", "webpack.config.js"]</div>
                        </details>
                    </div>
                </div>
            `;
        }

        function showError() {
            const area = document.getElementById('demo-area');
            area.innerHTML = `
                <div class="widget-container">
                    <h3>❌ Error Demo</h3>
                    <div class="mcp-inline-tool-widget error">
                        <div class="mcp-widget-header">
                            <div class="mcp-widget-title">
                                <span class="mcp-widget-icon">❌</span>
                                <span>MCP Tool Error: filesystem/list-files</span>
                            </div>
                            <button class="mcp-close-btn" onclick="this.closest('.widget-container').remove()">✖</button>
                        </div>
                        <div class="mcp-status-section">
                            <div class="mcp-status-row">
                                <strong>Status:</strong> 
                                <span>❌ Failed</span>
                            </div>
                            <div class="mcp-status-row">
                                <strong>Time:</strong> 
                                <span>${new Date().toLocaleTimeString()}</span>
                            </div>
                            <div class="mcp-status-row">
                                <strong>Duration:</strong> 
                                <span>2.1s</span>
                            </div>
                        </div>
                        <details class="mcp-details">
                            <summary>Error Details</summary>
                            <div class="mcp-result-content">Error: Permission denied: Unable to access the specified directory

Stack trace:
  at FileSystem.listFiles (filesystem.js:42)
  at mcpServer.handleRequest (server.js:156)
  at timeout (bridge.js:89)</div>
                        </details>
                    </div>
                </div>
            `;
        }

        function showAll() {
            const area = document.getElementById('demo-area');
            area.innerHTML = `
                <div class="widget-container">
                    <h3>🔄 Loading State</h3>
                    <div class="mcp-inline-tool-widget loading">
                        <div class="mcp-widget-header">
                            <div class="mcp-widget-title">
                                <div class="mcp-loading-dots">
                                    <div></div><div></div><div></div>
                                </div>
                                <span>Executing MCP Tool: github/search-repos</span>
                            </div>
                            <button class="mcp-close-btn" onclick="this.parentElement.parentElement.parentElement.style.display='none'">✖</button>
                        </div>
                        <div class="mcp-status-section">
                            <div class="mcp-status-row">
                                <strong>Status:</strong> 
                                <span>⏳ Executing...</span>
                            </div>
                            <div class="mcp-status-row">
                                <strong>Started:</strong> 
                                <span>${new Date().toLocaleTimeString()}</span>
                            </div>
                        </div>
                        <details class="mcp-details">
                            <summary>Request Details</summary>
                            <div class="mcp-result-content">{
  "serverId": "github",
  "toolName": "search-repos",
  "parameters": {
    "query": "javascript framework",
    "limit": 5
  }
}</div>
                        </details>
                    </div>
                </div>

                <div class="widget-container">
                    <h3>✅ Success State</h3>
                    <div class="mcp-inline-tool-widget success">
                        <div class="mcp-widget-header">
                            <div class="mcp-widget-title">
                                <span class="mcp-widget-icon">✅</span>
                                <span>MCP Tool Result: github/search-repos</span>
                            </div>
                            <button class="mcp-close-btn" onclick="this.parentElement.parentElement.parentElement.style.display='none'">✖</button>
                        </div>
                        <div class="mcp-status-section">
                            <div class="mcp-status-row">
                                <strong>Status:</strong> 
                                <span>✅ Success</span>
                            </div>
                            <div class="mcp-status-row">
                                <strong>Executed:</strong> 
                                <span>${new Date().toLocaleTimeString()}</span>
                            </div>
                            <div class="mcp-status-row">
                                <strong>Duration:</strong> 
                                <span>1.8s</span>
                            </div>
                        </div>
                        <details class="mcp-details">
                            <summary>Result (1,250 characters)</summary>
                            <div class="mcp-result-content">Found 3 repositories:

1. microsoft/vscode
   ⭐ 162,000 stars
   📝 Visual Studio Code
   🏷️ TypeScript, JavaScript

2. facebook/react
   ⭐ 225,000 stars  
   📝 A declarative, efficient, and flexible JavaScript library
   🏷️ JavaScript, React

3. vercel/next.js
   ⭐ 123,000 stars
   📝 The React Framework for Production
   🏷️ JavaScript, TypeScript, React</div>
                        </details>
                    </div>
                </div>

                <div class="widget-container">
                    <h3>❌ Error State</h3>
                    <div class="mcp-inline-tool-widget error">
                        <div class="mcp-widget-header">
                            <div class="mcp-widget-title">
                                <span class="mcp-widget-icon">❌</span>
                                <span>MCP Tool Error: github/search-repos</span>
                            </div>
                            <button class="mcp-close-btn" onclick="this.parentElement.parentElement.parentElement.style.display='none'">✖</button>
                        </div>
                        <div class="mcp-status-section">
                            <div class="mcp-status-row">
                                <strong>Status:</strong> 
                                <span>❌ Failed</span>
                            </div>
                            <div class="mcp-status-row">
                                <strong>Time:</strong> 
                                <span>${new Date().toLocaleTimeString()}</span>
                            </div>
                            <div class="mcp-status-row">
                                <strong>Duration:</strong> 
                                <span>5.2s</span>
                            </div>
                        </div>
                        <details class="mcp-details">
                            <summary>Error Details</summary>
                            <div class="mcp-result-content">Error: GitHub API rate limit exceeded. Please try again in 60 minutes.

Response status: 403
Rate limit reset: ${new Date(Date.now() + 3600000).toLocaleTimeString()}
Remaining requests: 0/5000</div>
                        </details>
                    </div>
                </div>
            `;
        }

        // Stopwatch functionality for demo
        function startStopwatch(element) {
            const startTime = Date.now();
            const stopwatchElement = element.querySelector('.mcp-stopwatch');
            if (!stopwatchElement) return;

            const updateStopwatch = () => {
                const elapsed = (Date.now() - startTime) / 1000;
                stopwatchElement.textContent = `${elapsed.toFixed(1)}s`;
                
                // Add subtle pulse effect
                stopwatchElement.style.color = elapsed % 2 < 1 ? '#ffffff' : '#e0e0e0';
            };

            const intervalId = setInterval(updateStopwatch, 100);
            element.stopwatchInterval = intervalId;
            
            // Auto-cleanup after demo
            setTimeout(() => {
                if (element.stopwatchInterval === intervalId) {
                    clearInterval(intervalId);
                }
            }, 30000);
        }

        // Start stopwatches for all loading widgets
        function startAllStopwatches() {
            document.querySelectorAll('.loading').forEach(widget => {
                startStopwatch(widget);
            });
        }

        // Show all states by default
        showAll();
        
        // Start stopwatches after a brief delay
        setTimeout(startAllStopwatches, 100);
    </script>
</body>
</html>