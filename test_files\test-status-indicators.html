<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test MCP Status Indicators</title>
    <link rel="stylesheet" href="extension/css/mcp-interface.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #ffffff;
            min-height: 100vh;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        h1 {
            color: #00d4aa;
            text-align: center;
            margin-bottom: 30px;
        }

        .test-section {
            background: #2a2a2a;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #333;
        }

        .test-section h2 {
            color: #00d4aa;
            margin-top: 0;
        }

        .test-button {
            background: #00d4aa;
            color: #1a1a1a;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-weight: 500;
        }

        .test-button:hover {
            background: #00c29a;
        }

        .status-demo {
            display: flex;
            gap: 20px;
            align-items: center;
            margin: 20px 0;
        }

        .mock-header {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            position: relative;
            margin-bottom: 20px;
        }

        #connection-status {
            color: #ffd700;
            font-weight: bold;
        }

        .instructions {
            background: #1e3a5f;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #00d4aa;
        }
    </style>
</head>

<body>
    <div class="test-container">
        <h1>🔧 MCP Status Indicators Test</h1>

        <div class="instructions">
            <strong>Test Instructions:</strong>
            <ul>
                <li>This page simulates the status indicators that will appear on Perplexity.ai</li>
                <li>Look for the status indicator in the top-left corner (header area)</li>
                <li>Look for the floating status panel in the bottom-right corner</li>
                <li>Hover over both indicators to see tooltips with detailed information</li>
                <li>Use the buttons below to simulate different connection states</li>
            </ul>
        </div>

        <div class="mock-header">
            <strong>Mock Perplexity Header</strong>
            <span id="connection-status">Initializing MCP...</span>
        </div>

        <div class="test-section">
            <h2>Connection State Testing</h2>
            <p>Test different connection states to see how the indicators respond:</p>
            <button class="test-button" onclick="simulateConnected()">Simulate Connected</button>
            <button class="test-button" onclick="simulateDisconnected()">Simulate Disconnected</button>
            <button class="test-button" onclick="simulateConnecting()">Simulate Connecting</button>
            <button class="test-button" onclick="addMockServers()">Add Mock Servers</button>
            <button class="test-button" onclick="clearServers()">Clear Servers</button>
        </div>

        <div class="test-section">
            <h2>UI Component Testing</h2>
            <p>Test the visibility and functionality of UI components:</p>
            <button class="test-button" onclick="toggleStatusIndicators()">Toggle Status Indicators</button>
            <button class="test-button" onclick="testTooltips()">Test Tooltips</button>
            <button class="test-button" onclick="testQuickActions()">Test Quick Actions</button>
        </div>

        <div class="test-section">
            <h2>Current Status</h2>
            <div id="status-info">
                <p><strong>Connection:</strong> <span id="current-connection">Not initialized</span></p>
                <p><strong>Servers:</strong> <span id="current-servers">0</span></p>
                <p><strong>Tools:</strong> <span id="current-tools">0</span></p>
            </div>
        </div>
    </div>

    <script>
        // Mock MCP client for testing
        const mockMcpClient = {
            isConnected: false,
            mcpServers: [],
            settings: {
                serverSettings: new Map()
            },

            getConnectedToolsCount() {
                return this.mcpServers.reduce((count, server) => {
                    return count + (server.tools ? server.tools.length : 0);
                }, 0);
            },

            addStatusIndicators() {
                this.addMainStatusIndicator();
                this.addFloatingStatusPanel();
            },

            addMainStatusIndicator() {
                // Check if already exists
                if (document.getElementById('mcp-header-status')) return;

                const headerElement = document.querySelector('.mock-header') || document.body;

                const statusIndicator = document.createElement('div');
                statusIndicator.id = 'mcp-header-status';
                statusIndicator.className = 'mcp-header-status';
                statusIndicator.innerHTML = `
                    <div class="mcp-status-icon" title="MCP Tools Status">
                        <span class="status-dot" id="mcp-status-dot"></span>
                        <span class="status-text">🔧</span>
                    </div>
                    <div class="mcp-status-tooltip" id="mcp-status-tooltip">
                        <div class="tooltip-header">MCP Bridge Status</div>
                        <div class="tooltip-content" id="mcp-tooltip-content">
                            <div class="status-line">
                                <span class="label">Connection:</span>
                                <span class="value" id="tooltip-connection">Connecting...</span>
                            </div>
                            <div class="status-line">
                                <span class="label">Servers:</span>
                                <span class="value" id="tooltip-servers">0</span>
                            </div>
                            <div class="status-line">
                                <span class="label">Tools:</span>
                                <span class="value" id="tooltip-tools">0</span>
                            </div>
                            <div class="server-list" id="tooltip-server-list"></div>
                        </div>
                    </div>
                `;

                headerElement.appendChild(statusIndicator);
                console.log('✅ Added header status indicator');
            },

            addFloatingStatusPanel() {
                // Check if already exists
                if (document.getElementById('mcp-floating-status')) return;

                const floatingPanel = document.createElement('div');
                floatingPanel.id = 'mcp-floating-status';
                floatingPanel.className = 'mcp-floating-status';
                floatingPanel.innerHTML = `
                    <div class="floating-status-content">
                        <div class="status-header">
                            <span class="status-title">MCP</span>
                            <span class="status-count" id="floating-tool-count">0</span>
                        </div>
                        <div class="quick-actions">
                            <button class="quick-action-btn" id="floating-toggle-panel" title="Toggle Tools Panel">
                                <span>📋</span>
                            </button>
                            <button class="quick-action-btn" id="floating-toggle-debug" title="Toggle Debug Panel">
                                <span>🔧</span>
                            </button>
                        </div>
                    </div>
                    <div class="floating-tooltip" id="floating-tooltip">
                        <div class="tooltip-content">
                            <div class="status-overview" id="floating-overview"></div>
                            <div class="server-overview" id="floating-servers"></div>
                        </div>
                    </div>
                `;

                document.body.appendChild(floatingPanel);

                // Add event listeners
                document.getElementById('floating-toggle-panel').onclick = () => {
                    alert('Toggle Panel button clicked!');
                };
                document.getElementById('floating-toggle-debug').onclick = () => {
                    alert('Toggle Debug button clicked!');
                };

                console.log('✅ Added floating status panel');
            },

            updateStatusIndicators() {
                this.updateHeaderStatus();
                this.updateFloatingStatus();
                this.updateTestStatus();
            },

            updateHeaderStatus() {
                const statusDot = document.getElementById('mcp-status-dot');
                const tooltipConnection = document.getElementById('tooltip-connection');
                const tooltipServers = document.getElementById('tooltip-servers');
                const tooltipTools = document.getElementById('tooltip-tools');
                const serverList = document.getElementById('tooltip-server-list');

                if (!statusDot) return;

                // Update status dot
                statusDot.className = `status-dot ${this.isConnected ? 'connected' : 'disconnected'}`;

                // Update tooltip content
                if (tooltipConnection) {
                    tooltipConnection.textContent = this.isConnected ? 'Connected' : 'Disconnected';
                    tooltipConnection.className = `value ${this.isConnected ? 'connected' : 'disconnected'}`;
                }

                if (tooltipServers) {
                    tooltipServers.textContent = this.mcpServers.length;
                }

                if (tooltipTools) {
                    tooltipTools.textContent = this.getConnectedToolsCount();
                }

                // Update server list
                if (serverList) {
                    serverList.innerHTML = this.mcpServers.map(server => {
                        const toolCount = server.tools ? server.tools.length : 0;
                        const isEnabled = this.settings.serverSettings.get(server.id)?.enabled !== false;
                        return `
                            <div class="server-item ${isEnabled ? 'enabled' : 'disabled'}">
                                <span class="server-name">${server.name || server.id}</span>
                                <span class="server-tools">${toolCount} tools</span>
                                <span class="server-status ${isEnabled ? 'active' : 'inactive'}">
                                    ${isEnabled ? '●' : '○'}
                                </span>
                            </div>
                        `;
                    }).join('');
                }
            },

            updateFloatingStatus() {
                const toolCount = document.getElementById('floating-tool-count');
                const overview = document.getElementById('floating-overview');
                const servers = document.getElementById('floating-servers');

                if (toolCount) {
                    toolCount.textContent = this.getConnectedToolsCount();
                }

                if (overview) {
                    overview.innerHTML = `
                        <div class="overview-item">
                            <span class="overview-label">Status:</span>
                            <span class="overview-value ${this.isConnected ? 'connected' : 'disconnected'}">
                                ${this.isConnected ? 'Connected' : 'Disconnected'}
                            </span>
                        </div>
                        <div class="overview-item">
                            <span class="overview-label">Total Tools:</span>
                            <span class="overview-value">${this.getConnectedToolsCount()}</span>
                        </div>
                    `;
                }

                if (servers) {
                    servers.innerHTML = `
                        <div class="servers-header">Servers (${this.mcpServers.length})</div>
                        ${this.mcpServers.map(server => {
                        const toolCount = server.tools ? server.tools.length : 0;
                        const isEnabled = this.settings.serverSettings.get(server.id)?.enabled !== false;
                        return `
                                <div class="server-summary ${isEnabled ? 'enabled' : 'disabled'}">
                                    <span class="server-name">${server.name || server.id}</span>
                                    <span class="server-info">${toolCount} tools</span>
                                </div>
                            `;
                    }).join('')}
                    `;
                }
            },

            updateTestStatus() {
                document.getElementById('current-connection').textContent = this.isConnected ? 'Connected' : 'Disconnected';
                document.getElementById('current-servers').textContent = this.mcpServers.length;
                document.getElementById('current-tools').textContent = this.getConnectedToolsCount();
                document.getElementById('connection-status').textContent =
                    this.isConnected ? 'MCP Connected ✅' : 'MCP Disconnected ❌';
            }
        };

        // Test functions
        function simulateConnected() {
            mockMcpClient.isConnected = true;
            mockMcpClient.updateStatusIndicators();
            console.log('Simulated connected state');
        }

        function simulateDisconnected() {
            mockMcpClient.isConnected = false;
            mockMcpClient.updateStatusIndicators();
            console.log('Simulated disconnected state');
        }

        function simulateConnecting() {
            mockMcpClient.isConnected = false;
            const statusDot = document.getElementById('mcp-status-dot');
            if (statusDot) {
                statusDot.className = 'status-dot connecting';
            }
            document.getElementById('connection-status').textContent = 'MCP Connecting...';
            setTimeout(() => simulateConnected(), 2000);
        }

        function addMockServers() {
            mockMcpClient.mcpServers = [
                {
                    id: 'filesystem',
                    name: 'File System Tools',
                    tools: [
                        { name: 'read_file' },
                        { name: 'write_file' },
                        { name: 'list_directory' }
                    ]
                },
                {
                    id: 'weather',
                    name: 'Weather API',
                    tools: [
                        { name: 'get_weather' },
                        { name: 'get_forecast' }
                    ]
                },
                {
                    id: 'database',
                    name: 'Database Tools',
                    tools: [
                        { name: 'query_db' },
                        { name: 'update_record' },
                        { name: 'create_table' },
                        { name: 'backup_db' }
                    ]
                }
            ];

            mockMcpClient.settings.serverSettings.set('filesystem', { enabled: true });
            mockMcpClient.settings.serverSettings.set('weather', { enabled: true });
            mockMcpClient.settings.serverSettings.set('database', { enabled: false });

            mockMcpClient.updateStatusIndicators();
            console.log('Added mock servers');
        }

        function clearServers() {
            mockMcpClient.mcpServers = [];
            mockMcpClient.settings.serverSettings.clear();
            mockMcpClient.updateStatusIndicators();
            console.log('Cleared servers');
        }

        function toggleStatusIndicators() {
            const header = document.getElementById('mcp-header-status');
            const floating = document.getElementById('mcp-floating-status');

            if (header && floating) {
                header.style.display = header.style.display === 'none' ? 'block' : 'none';
                floating.style.display = floating.style.display === 'none' ? 'block' : 'none';
            } else {
                mockMcpClient.addStatusIndicators();
                mockMcpClient.updateStatusIndicators();
            }
        }

        function testTooltips() {
            const tooltips = document.querySelectorAll('.mcp-status-tooltip, .floating-tooltip');
            tooltips.forEach(tooltip => {
                tooltip.style.opacity = '1';
                tooltip.style.visibility = 'visible';
                setTimeout(() => {
                    tooltip.style.opacity = '';
                    tooltip.style.visibility = '';
                }, 3000);
            });
        }

        function testQuickActions() {
            const buttons = document.querySelectorAll('.quick-action-btn');
            buttons.forEach(button => {
                button.style.transform = 'scale(1.1)';
                button.style.background = '#00d4aa';
                setTimeout(() => {
                    button.style.transform = '';
                    button.style.background = '';
                }, 500);
            });
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function () {
            console.log('Initializing MCP Status Indicators Test');
            mockMcpClient.addStatusIndicators();
            mockMcpClient.updateStatusIndicators();

            // Show instructions
            setTimeout(() => {
                alert('MCP Status Indicators Test loaded! Look for:\n\n' +
                    '1. Header status indicator (top-left, 🔧 icon)\n' +
                    '2. Floating status panel (bottom-right)\n' +
                    '3. Hover over both for detailed tooltips\n' +
                    '4. Use the test buttons to simulate different states');
            }, 500);
        });
    </script>
</body>

</html>