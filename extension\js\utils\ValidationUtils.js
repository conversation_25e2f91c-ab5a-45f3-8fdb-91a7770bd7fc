/**
 * ValidationUtils - Input validation and sanitization utilities
 * 
 * Provides comprehensive validation, sanitization, and type checking
 * with security-focused input handling.
 * 
 * @version 2.0.0
 */

class ValidationUtils {
    constructor() {
        this.isInitialized = false;
        this.validationRules = new Map();
        this.sanitizationRules = new Map();
        this.setupDefaultRules();
    }

    /**
     * Initialize validation utilities
     * @param {ErrorHandler} errorHandler - Error handler instance
     */
    initialize(errorHandler) {
        this.errorHandler = errorHandler;
        this.isInitialized = true;
        console.log('[ValidationUtils] Initialized successfully');
    }

    /**
     * Set up default validation and sanitization rules
     * @private
     */
    setupDefaultRules() {
        // Default validation rules
        this.validationRules.set('email', /^[^\s@]+@[^\s@]+\.[^\s@]+$/);
        this.validationRules.set('url', /^https?:\/\/[^\s/$.?#].[^\s]*$/i);
        this.validationRules.set('serverId', /^[a-zA-Z0-9_-]+$/);
        this.validationRules.set('toolName', /^[a-zA-Z0-9_.-]+$/);
        this.validationRules.set('threadId', /^[a-zA-Z0-9-]+$/);
        this.validationRules.set('alphanumeric', /^[a-zA-Z0-9]+$/);
        this.validationRules.set('numeric', /^\d+$/);
        this.validationRules.set('boolean', /^(true|false)$/i);

        // Default sanitization rules
        this.sanitizationRules.set('html', (input) => this.sanitizeHTML(input));
        this.sanitizationRules.set('script', (input) => this.removeScripts(input));
        this.sanitizationRules.set('whitespace', (input) => this.normalizeWhitespace(input));
        this.sanitizationRules.set('filename', (input) => this.sanitizeFilename(input));
    }

    /**
     * Validate input against a rule
     * @param {any} input - Input to validate
     * @param {string|RegExp|Function} rule - Validation rule
     * @param {Object} options - Validation options
     * @returns {Object} Validation result
     */
    validate(input, rule, options = {}) {
        try {
            const {
                required = false,
                allowEmpty = false,
                customMessage = null
            } = options;

            // Check if input is required
            if (required && (input === null || input === undefined)) {
                return {
                    isValid: false,
                    error: customMessage || 'Input is required',
                    value: input
                };
            }

            // Handle empty values
            if (!allowEmpty && this.isEmpty(input)) {
                return required ? {
                    isValid: false,
                    error: customMessage || 'Input cannot be empty',
                    value: input
                } : {
                    isValid: true,
                    value: input
                };
            }

            // Get validation rule
            let validationRule = rule;
            if (typeof rule === 'string') {
                validationRule = this.validationRules.get(rule);
                if (!validationRule) {
                    return {
                        isValid: false,
                        error: `Unknown validation rule: ${rule}`,
                        value: input
                    };
                }
            }

            // Apply validation
            let isValid = false;
            if (validationRule instanceof RegExp) {
                isValid = validationRule.test(String(input));
            } else if (typeof validationRule === 'function') {
                isValid = validationRule(input);
            } else {
                return {
                    isValid: false,
                    error: 'Invalid validation rule type',
                    value: input
                };
            }

            return {
                isValid,
                error: isValid ? null : (customMessage || 'Validation failed'),
                value: input
            };

        } catch (error) {
            this.errorHandler?.handleError(error, 'validate_input', { input, rule, options });
            return {
                isValid: false,
                error: 'Validation error occurred',
                value: input
            };
        }
    }

    /**
     * Validate multiple inputs against rules
     * @param {Object} inputs - Object with input values
     * @param {Object} rules - Object with validation rules
     * @returns {Object} Validation results
     */
    validateMultiple(inputs, rules) {
        const results = {
            isValid: true,
            errors: {},
            values: {}
        };

        try {
            for (const [key, rule] of Object.entries(rules)) {
                const input = inputs[key];
                const validation = this.validate(input, rule.rule || rule, rule.options || {});
                
                results.values[key] = validation.value;
                
                if (!validation.isValid) {
                    results.isValid = false;
                    results.errors[key] = validation.error;
                }
            }

        } catch (error) {
            this.errorHandler?.handleError(error, 'validate_multiple', { inputs, rules });
            results.isValid = false;
            results.errors.general = 'Validation error occurred';
        }

        return results;
    }

    /**
     * Sanitize input using specified rules
     * @param {any} input - Input to sanitize
     * @param {string|string[]|Function} rules - Sanitization rules
     * @returns {any} Sanitized input
     */
    sanitize(input, rules) {
        try {
            if (input === null || input === undefined) {
                return input;
            }

            let sanitized = input;

            // Handle array of rules
            if (Array.isArray(rules)) {
                for (const rule of rules) {
                    sanitized = this.sanitize(sanitized, rule);
                }
                return sanitized;
            }

            // Handle string rule
            if (typeof rules === 'string') {
                const sanitizationRule = this.sanitizationRules.get(rules);
                if (sanitizationRule) {
                    return sanitizationRule(sanitized);
                }
                return sanitized;
            }

            // Handle function rule
            if (typeof rules === 'function') {
                return rules(sanitized);
            }

            return sanitized;

        } catch (error) {
            this.errorHandler?.handleError(error, 'sanitize_input', { input, rules });
            return input;
        }
    }

    /**
     * Type checking utilities
     */

    /**
     * Check if value is empty
     * @param {any} value - Value to check
     * @returns {boolean} True if empty
     */
    isEmpty(value) {
        if (value === null || value === undefined) return true;
        if (typeof value === 'string') return value.trim() === '';
        if (Array.isArray(value)) return value.length === 0;
        if (typeof value === 'object') return Object.keys(value).length === 0;
        return false;
    }

    /**
     * Check if value is a valid string
     * @param {any} value - Value to check
     * @param {Object} options - Validation options
     * @returns {boolean} True if valid string
     */
    isValidString(value, options = {}) {
        const { minLength = 0, maxLength = Infinity, allowEmpty = true } = options;
        
        if (typeof value !== 'string') return false;
        if (!allowEmpty && value.trim() === '') return false;
        if (value.length < minLength || value.length > maxLength) return false;
        
        return true;
    }

    /**
     * Check if value is a valid number
     * @param {any} value - Value to check
     * @param {Object} options - Validation options
     * @returns {boolean} True if valid number
     */
    isValidNumber(value, options = {}) {
        const { min = -Infinity, max = Infinity, integer = false } = options;
        
        const num = Number(value);
        if (isNaN(num) || !isFinite(num)) return false;
        if (integer && !Number.isInteger(num)) return false;
        if (num < min || num > max) return false;
        
        return true;
    }

    /**
     * Check if value is a valid boolean
     * @param {any} value - Value to check
     * @returns {boolean} True if valid boolean
     */
    isValidBoolean(value) {
        return typeof value === 'boolean' || 
               (typeof value === 'string' && /^(true|false)$/i.test(value));
    }

    /**
     * Check if value is a valid array
     * @param {any} value - Value to check
     * @param {Object} options - Validation options
     * @returns {boolean} True if valid array
     */
    isValidArray(value, options = {}) {
        const { minLength = 0, maxLength = Infinity, itemValidator = null } = options;
        
        if (!Array.isArray(value)) return false;
        if (value.length < minLength || value.length > maxLength) return false;
        
        if (itemValidator) {
            return value.every(item => itemValidator(item));
        }
        
        return true;
    }

    /**
     * Check if value is a valid object
     * @param {any} value - Value to check
     * @param {Object} options - Validation options
     * @returns {boolean} True if valid object
     */
    isValidObject(value, options = {}) {
        const { allowNull = false, requiredKeys = [], schema = null } = options;
        
        if (value === null) return allowNull;
        if (typeof value !== 'object' || Array.isArray(value)) return false;
        
        // Check required keys
        for (const key of requiredKeys) {
            if (!(key in value)) return false;
        }
        
        // Validate against schema
        if (schema) {
            for (const [key, validator] of Object.entries(schema)) {
                if (key in value && !validator(value[key])) {
                    return false;
                }
            }
        }
        
        return true;
    }

    /**
     * Sanitization methods
     */

    /**
     * Sanitize HTML content
     * @private
     * @param {string} input - HTML input
     * @returns {string} Sanitized HTML
     */
    sanitizeHTML(input) {
        if (typeof input !== 'string') return input;
        
        // Remove script tags and their content
        let sanitized = input.replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '');
        
        // Remove dangerous attributes
        sanitized = sanitized.replace(/\s*on\w+\s*=\s*["'][^"']*["']/gi, '');
        sanitized = sanitized.replace(/\s*javascript\s*:/gi, '');
        
        // Remove dangerous tags
        const dangerousTags = ['iframe', 'object', 'embed', 'form', 'input', 'textarea', 'select'];
        for (const tag of dangerousTags) {
            const regex = new RegExp(`<${tag}\\b[^>]*>.*?<\\/${tag}>`, 'gi');
            sanitized = sanitized.replace(regex, '');
        }
        
        return sanitized;
    }

    /**
     * Remove script content
     * @private
     * @param {string} input - Input string
     * @returns {string} String without scripts
     */
    removeScripts(input) {
        if (typeof input !== 'string') return input;
        
        return input
            .replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi, '')
            .replace(/javascript:/gi, '')
            .replace(/on\w+\s*=/gi, '');
    }

    /**
     * Normalize whitespace
     * @private
     * @param {string} input - Input string
     * @returns {string} Normalized string
     */
    normalizeWhitespace(input) {
        if (typeof input !== 'string') return input;
        
        return input
            .replace(/\s+/g, ' ')
            .trim();
    }

    /**
     * Sanitize filename
     * @private
     * @param {string} input - Filename input
     * @returns {string} Sanitized filename
     */
    sanitizeFilename(input) {
        if (typeof input !== 'string') return input;
        
        return input
            .replace(/[<>:"/\\|?*\x00-\x1f]/g, '')
            .replace(/^\.+/, '')
            .substring(0, 255);
    }

    /**
     * Validate and sanitize MCP tool call parameters
     * @param {Object} parameters - Tool parameters
     * @returns {Object} Validation and sanitization result
     */
    validateToolParameters(parameters) {
        try {
            if (!this.isValidObject(parameters, { allowNull: false })) {
                return {
                    isValid: false,
                    error: 'Parameters must be a valid object',
                    sanitized: {}
                };
            }

            const sanitized = {};
            const errors = [];

            for (const [key, value] of Object.entries(parameters)) {
                // Validate key
                if (!this.isValidString(key, { minLength: 1, maxLength: 100 })) {
                    errors.push(`Invalid parameter key: ${key}`);
                    continue;
                }

                // Sanitize key
                const sanitizedKey = this.sanitize(key, ['whitespace']);

                // Sanitize value based on type
                let sanitizedValue = value;
                if (typeof value === 'string') {
                    sanitizedValue = this.sanitize(value, ['script']);
                    
                    // Validate string length
                    if (sanitizedValue.length > 10000) {
                        errors.push(`Parameter ${key} is too long`);
                        continue;
                    }
                }

                sanitized[sanitizedKey] = sanitizedValue;
            }

            return {
                isValid: errors.length === 0,
                error: errors.length > 0 ? errors.join(', ') : null,
                sanitized
            };

        } catch (error) {
            this.errorHandler?.handleError(error, 'validate_tool_parameters', { parameters });
            return {
                isValid: false,
                error: 'Parameter validation failed',
                sanitized: {}
            };
        }
    }

    /**
     * Validate MCP server configuration
     * @param {Object} config - Server configuration
     * @returns {Object} Validation result
     */
    validateServerConfig(config) {
        const rules = {
            id: {
                rule: 'serverId',
                options: { required: true, customMessage: 'Server ID must be alphanumeric with hyphens/underscores' }
            },
            name: {
                rule: (value) => this.isValidString(value, { minLength: 1, maxLength: 100 }),
                options: { required: true, customMessage: 'Server name must be 1-100 characters' }
            },
            command: {
                rule: (value) => this.isValidString(value, { minLength: 1, maxLength: 500 }),
                options: { required: true, customMessage: 'Command must be 1-500 characters' }
            },
            args: {
                rule: (value) => this.isValidArray(value, { itemValidator: (item) => typeof item === 'string' }),
                options: { required: false, customMessage: 'Arguments must be an array of strings' }
            },
            env: {
                rule: (value) => this.isValidObject(value),
                options: { required: false, customMessage: 'Environment must be an object' }
            }
        };

        return this.validateMultiple(config, rules);
    }

    /**
     * Add custom validation rule
     * @param {string} name - Rule name
     * @param {RegExp|Function} rule - Validation rule
     */
    addValidationRule(name, rule) {
        this.validationRules.set(name, rule);
    }

    /**
     * Add custom sanitization rule
     * @param {string} name - Rule name
     * @param {Function} rule - Sanitization function
     */
    addSanitizationRule(name, rule) {
        this.sanitizationRules.set(name, rule);
    }

    /**
     * Get validation statistics
     * @returns {Object} Validation statistics
     */
    getStats() {
        return {
            isInitialized: this.isInitialized,
            validationRules: this.validationRules.size,
            sanitizationRules: this.sanitizationRules.size
        };
    }

    /**
     * Cleanup validation utilities
     */
    cleanup() {
        console.log('[ValidationUtils] Starting cleanup...');
        
        try {
            this.validationRules.clear();
            this.sanitizationRules.clear();
            this.setupDefaultRules();
            this.isInitialized = false;
            
            console.log('[ValidationUtils] Cleanup completed');
        } catch (error) {
            console.error('[ValidationUtils] Error during cleanup:', error);
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = ValidationUtils;
} else {
    window.ValidationUtils = ValidationUtils;
}
