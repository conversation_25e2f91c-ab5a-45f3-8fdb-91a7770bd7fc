<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Unified MCP Status Display</title>
    <link rel="stylesheet" href="extension/css/mcp-interface.css">
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #1a1a1a;
            color: #ffffff;
            min-height: 100vh;
        }

        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }

        h1 {
            color: #00d4aa;
            text-align: center;
            margin-bottom: 30px;
        }

        .test-section {
            background: #2a2a2a;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #333;
        }

        .test-section h2 {
            color: #00d4aa;
            margin-top: 0;
        }

        .test-button {
            background: #00d4aa;
            color: #1a1a1a;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-weight: 500;
        }

        .test-button:hover {
            background: #00c29a;
        }

        .instructions {
            background: #1e3a5f;
            padding: 15px;
            border-radius: 6px;
            border-left: 4px solid #00d4aa;
        }

        .status-demo {
            display: flex;
            gap: 20px;
            align-items: center;
            margin: 20px 0;
        }

        .mock-page {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            position: relative;
            margin-bottom: 20px;
            min-height: 200px;
        }

        .mock-content {
            color: #ccc;
            font-size: 14px;
        }
    </style>
</head>

<body>
    <div class="test-container">
        <h1>🔧 Unified MCP Status Display Test</h1>

        <div class="instructions">
            <strong>Test Instructions:</strong>
            <ul>
                <li>This page tests the unified MCP status display in the top-right corner</li>
                <li>Look for: "🔧 MCP Tools ● Connected 11 MCP tools available" in the top-right</li>
                <li>Hover over the status to see a tooltip with available tools</li>
                <li>Use the buttons below to simulate different states</li>
                <li>The status should remain in the top-right area (not near search)</li>
            </ul>
        </div>

        <div class="test-section">
            <h2>Mock Perplexity Page</h2>
            <div class="mock-page">
                <div class="mock-content">
                    <p>This simulates the Perplexity.ai interface.</p>
                    <p>The unified MCP status should appear in the top-right corner above.</p>
                    <p>No complex debug panels or sidebars should be visible.</p>
                </div>
            </div>
        </div>

        <div class="test-section">
            <h2>Status Control Buttons</h2>
            <button class="test-button" onclick="simulateConnected()">✅ Simulate Connected</button>
            <button class="test-button" onclick="simulateDisconnected()">❌ Simulate Disconnected</button>
            <button class="test-button" onclick="simulateConnecting()">⏳ Simulate Connecting</button>
            <button class="test-button" onclick="addMockServers()">📦 Add Mock Servers</button>
            <button class="test-button" onclick="clearServers()">🗑️ Clear Servers</button>
            <button class="test-button" onclick="testTooltip()">🔍 Test Tooltip</button>
        </div>

        <div class="test-section">
            <h2>Current Status</h2>
            <div id="status-info">
                <p><strong>Connection:</strong> <span id="current-connection">Not initialized</span></p>
                <p><strong>Servers:</strong> <span id="current-servers">0</span></p>
                <p><strong>Tools:</strong> <span id="current-tools">0</span></p>
            </div>
        </div>
    </div>

    <script>
        // Mock MCP client for testing the unified status
        const mockMcpClient = {
            isConnected: false,
            mcpServers: [],
            settings: {
                serverSettings: new Map()
            },

            getConnectedToolsCount() {
                return this.mcpServers.reduce((count, server) => {
                    return count + (server.tools ? server.tools.length : 0);
                }, 0);
            },

            addMcpToolsStatus() {
                // Add clean "MCP Tools" status to top-right area
                if (document.getElementById('mcp-tools-status')) {
                    return; // Already exists
                }

                const statusElement = document.createElement('div');
                statusElement.id = 'mcp-tools-status';
                statusElement.className = 'mcp-tools-status';
                statusElement.innerHTML = `
                <span class="mcp-label" style="
                    padding-bottom: 9px;
                ">Perplexity Web MCP Bridge</span>
                    <div class="mcp-tools-header">
                        <span class="connection-dot" id="mcp-connection-dot"></span>
                        <span class="connection-text" id="mcp-connection-text">Connected</span>
                        <span class="tools-count-badge" id="mcp-tools-count-badge">11 MCP tools available</span>
                    </div>
                    <div class="mcp-tools-tooltip" id="mcp-tools-tooltip">
                        <div class="tooltip-header">Available MCP Tools</div>
                        <div class="tooltip-tools-list" id="mcp-tooltip-tools-list">
                            <div class="loading">Loading tools...</div>
                        </div>
                    </div>
                `;

                document.body.appendChild(statusElement);
                console.log('✅ Added unified MCP Tools status');
            },

            updateMcpToolsStatus() {
                const connectionDot = document.getElementById('mcp-connection-dot');
                const connectionText = document.getElementById('mcp-connection-text');
                const toolsCountBadge = document.getElementById('mcp-tools-count-badge');
                const toolsList = document.getElementById('mcp-tooltip-tools-list');

                // Update connection status
                if (connectionDot) {
                    connectionDot.className = `connection-dot ${this.isConnected ? 'connected' : 'disconnected'}`;
                }

                if (connectionText) {
                    connectionText.textContent = this.isConnected ? 'Connected' : 'Disconnected';
                    connectionText.className = `connection-text ${this.isConnected ? 'connected' : 'disconnected'}`;
                }

                // Update tools count badge
                const toolCount = this.getConnectedToolsCount();
                if (toolsCountBadge) {
                    toolsCountBadge.textContent = `${toolCount} MCP tools available`;
                }

                // Update tooltip with available tools
                if (toolsList) {
                    if (this.mcpServers.length === 0) {
                        toolsList.innerHTML = '<div class="no-tools">No servers connected</div>';
                    } else {
                        const allTools = [];
                        this.mcpServers.forEach(server => {
                            if (server.tools && server.status === 'running') {
                                server.tools.forEach(tool => {
                                    allTools.push({
                                        serverName: server.name || server.id,
                                        toolName: tool.name,
                                        description: tool.description || 'No description'
                                    });
                                });
                            }
                        });

                        if (allTools.length === 0) {
                            toolsList.innerHTML = '<div class="no-tools">No tools available</div>';
                        } else {
                            // Limit to first 10 tools and add ellipsis if more
                            const displayTools = allTools.slice(0, 10);
                            const hasMore = allTools.length > 10;

                            toolsList.innerHTML = displayTools.map(tool => {
                                const shortDesc = tool.description.length > 50
                                    ? tool.description.substring(0, 50) + '...'
                                    : tool.description;
                                return `
                                    <div class="tool-item">
                                        <div class="tool-name">${tool.toolName}</div>
                                        <div class="tool-server">${tool.serverName}</div>
                                        <div class="tool-description">${shortDesc}</div>
                                    </div>
                                `;
                            }).join('') + (hasMore ? `<div class="more-tools">... and ${allTools.length - 10} more tools</div>` : '');
                        }
                    }
                }
            },

            updateTestStatus() {
                document.getElementById('current-connection').textContent = this.isConnected ? 'Connected' : 'Disconnected';
                document.getElementById('current-servers').textContent = this.mcpServers.length;
                document.getElementById('current-tools').textContent = this.getConnectedToolsCount();
            }
        };

        // Test functions
        function simulateConnected() {
            mockMcpClient.isConnected = true;
            mockMcpClient.updateMcpToolsStatus();
            mockMcpClient.updateTestStatus();
            console.log('Simulated connected state');
        }

        function simulateDisconnected() {
            mockMcpClient.isConnected = false;
            mockMcpClient.updateMcpToolsStatus();
            mockMcpClient.updateTestStatus();
            console.log('Simulated disconnected state');
        }

        function simulateConnecting() {
            mockMcpClient.isConnected = false;
            const connectionDot = document.getElementById('mcp-connection-dot');
            const connectionText = document.getElementById('mcp-connection-text');
            if (connectionDot) {
                connectionDot.className = 'connection-dot connecting';
            }
            if (connectionText) {
                connectionText.textContent = 'Connecting...';
                connectionText.className = 'connection-text connecting';
            }
            mockMcpClient.updateTestStatus();
            setTimeout(() => simulateConnected(), 2000);
        }

        function addMockServers() {
            mockMcpClient.mcpServers = [
                {
                    id: 'filesystem',
                    name: 'File System Tools',
                    status: 'running',
                    tools: [
                        { name: 'read_file', description: 'Read contents of a file from the filesystem' },
                        { name: 'write_file', description: 'Write content to a file on the filesystem' },
                        { name: 'list_directory', description: 'List files and directories in a given path' },
                        { name: 'create_directory', description: 'Create a new directory' },
                        { name: 'delete_file', description: 'Delete a file from the filesystem' }
                    ]
                },
                {
                    id: 'github',
                    name: 'GitHub API',
                    status: 'running',
                    tools: [
                        { name: 'search_repositories', description: 'Search for repositories on GitHub' },
                        { name: 'get_repository_info', description: 'Get detailed information about a repository' },
                        { name: 'list_issues', description: 'List issues for a repository' },
                        { name: 'create_issue', description: 'Create a new issue in a repository' },
                        { name: 'get_user_info', description: 'Get information about a GitHub user' },
                        { name: 'list_commits', description: 'List commits for a repository' }
                    ]
                }
            ];
            mockMcpClient.settings.serverSettings.set('filesystem', { enabled: true });
            mockMcpClient.settings.serverSettings.set('github', { enabled: true });
            mockMcpClient.updateMcpToolsStatus();
            mockMcpClient.updateTestStatus();
            console.log('Added mock servers with tools');
        }

        function clearServers() {
            mockMcpClient.mcpServers = [];
            mockMcpClient.settings.serverSettings.clear();
            mockMcpClient.updateMcpToolsStatus();
            mockMcpClient.updateTestStatus();
            console.log('Cleared servers');
        }

        function testTooltip() {
            const tooltip = document.getElementById('mcp-tools-tooltip');
            if (tooltip) {
                tooltip.style.opacity = '1';
                tooltip.style.visibility = 'visible';
                tooltip.style.transform = 'translateY(0)';
                setTimeout(() => {
                    tooltip.style.opacity = '';
                    tooltip.style.visibility = '';
                    tooltip.style.transform = '';
                }, 3000);
            }
        }

        // Initialize on page load
        document.addEventListener('DOMContentLoaded', function () {
            console.log('Initializing Unified MCP Status Test');
            mockMcpClient.addMcpToolsStatus();
            mockMcpClient.updateMcpToolsStatus();
            mockMcpClient.updateTestStatus();

            // Show instructions
            setTimeout(() => {
                alert('Unified MCP Status Test loaded! Look for:\n\n' +
                    '1. Unified status in top-right: "🔧 MCP Tools ● Connected 11 MCP tools available"\n' +
                    '2. Hover over the status for detailed tooltips\n' +
                    '3. No complex debug panels or sidebars\n' +
                    '4. Use the test buttons to simulate different states');
            }, 1000);
        });
    </script>
</body>

</html>