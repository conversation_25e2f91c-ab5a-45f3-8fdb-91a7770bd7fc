/**
 * DOMUtils - DOM manipulation utilities
 * 
 * Provides safe, efficient DOM manipulation methods with
 * proper error handling and performance optimizations.
 * 
 * @version 2.0.0
 */

class DOMUtils {
    constructor() {
        this.isInitialized = false;
        this.cache = new Map();
        this.cacheTimeout = 5000; // 5 seconds
    }

    /**
     * Initialize DOM utilities
     * @param {Error<PERSON>andler} errorHandler - Error handler instance
     */
    initialize(errorHandler) {
        this.errorHandler = errorHandler;
        this.isInitialized = true;
        console.log('[DOMUtils] Initialized successfully');
    }

    /**
     * Safely query selector with caching
     * @param {string} selector - CSS selector
     * @param {Element} context - Context element (default: document)
     * @param {boolean} useCache - Whether to use cache (default: true)
     * @returns {Element|null} Found element or null
     */
    querySelector(selector, context = document, useCache = true) {
        try {
            const cacheKey = `${selector}_${context === document ? 'document' : 'element'}`;
            
            if (useCache && this.cache.has(cacheKey)) {
                const cached = this.cache.get(cacheKey);
                if (Date.now() - cached.timestamp < this.cacheTimeout) {
                    // Verify element is still in DOM
                    if (cached.element && this.isElementInDOM(cached.element)) {
                        return cached.element;
                    }
                }
                this.cache.delete(cacheKey);
            }

            const element = context.querySelector(selector);
            
            if (useCache && element) {
                this.cache.set(cacheKey, {
                    element,
                    timestamp: Date.now()
                });
            }
            
            return element;
        } catch (error) {
            this.errorHandler?.handleError(error, 'querySelector', { selector, context });
            return null;
        }
    }

    /**
     * Safely query all selectors
     * @param {string} selector - CSS selector
     * @param {Element} context - Context element (default: document)
     * @returns {Element[]} Array of found elements
     */
    querySelectorAll(selector, context = document) {
        try {
            return Array.from(context.querySelectorAll(selector));
        } catch (error) {
            this.errorHandler?.handleError(error, 'querySelectorAll', { selector, context });
            return [];
        }
    }

    /**
     * Check if element is in DOM
     * @param {Element} element - Element to check
     * @returns {boolean} True if element is in DOM
     */
    isElementInDOM(element) {
        try {
            return element && document.body && document.body.contains(element);
        } catch (error) {
            return false;
        }
    }

    /**
     * Safely create element with attributes and content
     * @param {string} tagName - Tag name
     * @param {Object} attributes - Attributes to set
     * @param {string} content - Text content or HTML
     * @param {boolean} isHTML - Whether content is HTML (default: false)
     * @returns {Element|null} Created element or null
     */
    createElement(tagName, attributes = {}, content = '', isHTML = false) {
        try {
            const element = document.createElement(tagName);
            
            // Set attributes
            for (const [key, value] of Object.entries(attributes)) {
                if (key === 'style' && typeof value === 'object') {
                    Object.assign(element.style, value);
                } else if (key === 'dataset' && typeof value === 'object') {
                    Object.assign(element.dataset, value);
                } else {
                    element.setAttribute(key, String(value));
                }
            }
            
            // Set content
            if (content) {
                if (isHTML) {
                    element.innerHTML = content;
                } else {
                    element.textContent = content;
                }
            }
            
            return element;
        } catch (error) {
            this.errorHandler?.handleError(error, 'createElement', { tagName, attributes, content });
            return null;
        }
    }

    /**
     * Safely insert element into DOM
     * @param {Element} element - Element to insert
     * @param {Element} parent - Parent element
     * @param {Element|string} position - Position or reference element
     * @returns {boolean} True if successful
     */
    insertElement(element, parent, position = 'append') {
        try {
            if (!element || !parent || !this.isElementInDOM(parent)) {
                return false;
            }

            switch (position) {
                case 'append':
                    parent.appendChild(element);
                    break;
                case 'prepend':
                    parent.insertBefore(element, parent.firstChild);
                    break;
                case 'before':
                    parent.parentNode?.insertBefore(element, parent);
                    break;
                case 'after':
                    parent.parentNode?.insertBefore(element, parent.nextSibling);
                    break;
                default:
                    if (position instanceof Element) {
                        parent.insertBefore(element, position);
                    } else {
                        parent.appendChild(element);
                    }
            }
            
            return true;
        } catch (error) {
            this.errorHandler?.handleError(error, 'insertElement', { element, parent, position });
            return false;
        }
    }

    /**
     * Safely remove element from DOM
     * @param {Element} element - Element to remove
     * @returns {boolean} True if successful
     */
    removeElement(element) {
        try {
            if (element && element.parentNode) {
                element.parentNode.removeChild(element);
                return true;
            }
            return false;
        } catch (error) {
            this.errorHandler?.handleError(error, 'removeElement', { element });
            return false;
        }
    }

    /**
     * Get element's computed style property
     * @param {Element} element - Element to get style from
     * @param {string} property - CSS property name
     * @returns {string|null} Property value or null
     */
    getComputedStyleProperty(element, property) {
        try {
            if (!element || !this.isElementInDOM(element)) {
                return null;
            }
            
            const computedStyle = window.getComputedStyle(element);
            return computedStyle.getPropertyValue(property);
        } catch (error) {
            this.errorHandler?.handleError(error, 'getComputedStyleProperty', { element, property });
            return null;
        }
    }

    /**
     * Set multiple CSS properties safely
     * @param {Element} element - Element to style
     * @param {Object} styles - Style properties
     * @param {boolean} important - Whether to use !important
     * @returns {boolean} True if successful
     */
    setStyles(element, styles, important = false) {
        try {
            if (!element || !this.isElementInDOM(element)) {
                return false;
            }
            
            for (const [property, value] of Object.entries(styles)) {
                if (important) {
                    element.style.setProperty(property, value, 'important');
                } else {
                    element.style[property] = value;
                }
            }
            
            return true;
        } catch (error) {
            this.errorHandler?.handleError(error, 'setStyles', { element, styles });
            return false;
        }
    }

    /**
     * Get element's bounding rectangle safely
     * @param {Element} element - Element to get bounds for
     * @returns {DOMRect|null} Bounding rectangle or null
     */
    getBoundingRect(element) {
        try {
            if (!element || !this.isElementInDOM(element)) {
                return null;
            }
            
            return element.getBoundingClientRect();
        } catch (error) {
            this.errorHandler?.handleError(error, 'getBoundingRect', { element });
            return null;
        }
    }

    /**
     * Check if element is visible in viewport
     * @param {Element} element - Element to check
     * @param {number} threshold - Visibility threshold (0-1)
     * @returns {boolean} True if visible
     */
    isElementVisible(element, threshold = 0) {
        try {
            if (!element || !this.isElementInDOM(element)) {
                return false;
            }
            
            const rect = this.getBoundingRect(element);
            if (!rect) return false;
            
            const windowHeight = window.innerHeight || document.documentElement.clientHeight;
            const windowWidth = window.innerWidth || document.documentElement.clientWidth;
            
            const verticalVisible = (rect.top + rect.height * threshold) < windowHeight && 
                                  (rect.bottom - rect.height * threshold) > 0;
            const horizontalVisible = (rect.left + rect.width * threshold) < windowWidth && 
                                    (rect.right - rect.width * threshold) > 0;
            
            return verticalVisible && horizontalVisible;
        } catch (error) {
            this.errorHandler?.handleError(error, 'isElementVisible', { element, threshold });
            return false;
        }
    }

    /**
     * Scroll element into view safely
     * @param {Element} element - Element to scroll to
     * @param {Object} options - Scroll options
     * @returns {boolean} True if successful
     */
    scrollIntoView(element, options = { behavior: 'smooth', block: 'center' }) {
        try {
            if (!element || !this.isElementInDOM(element)) {
                return false;
            }
            
            element.scrollIntoView(options);
            return true;
        } catch (error) {
            this.errorHandler?.handleError(error, 'scrollIntoView', { element, options });
            return false;
        }
    }

    /**
     * Find closest parent element matching selector
     * @param {Element} element - Starting element
     * @param {string} selector - CSS selector
     * @returns {Element|null} Closest matching parent or null
     */
    closest(element, selector) {
        try {
            if (!element || !this.isElementInDOM(element)) {
                return null;
            }
            
            return element.closest(selector);
        } catch (error) {
            this.errorHandler?.handleError(error, 'closest', { element, selector });
            return null;
        }
    }

    /**
     * Get all parent elements up to root
     * @param {Element} element - Starting element
     * @param {Element} root - Root element (default: document.body)
     * @returns {Element[]} Array of parent elements
     */
    getParents(element, root = document.body) {
        const parents = [];
        
        try {
            let current = element?.parentElement;
            
            while (current && current !== root && this.isElementInDOM(current)) {
                parents.push(current);
                current = current.parentElement;
            }
        } catch (error) {
            this.errorHandler?.handleError(error, 'getParents', { element, root });
        }
        
        return parents;
    }

    /**
     * Clone element safely with optional deep clone
     * @param {Element} element - Element to clone
     * @param {boolean} deep - Whether to deep clone (default: true)
     * @returns {Element|null} Cloned element or null
     */
    cloneElement(element, deep = true) {
        try {
            if (!element) {
                return null;
            }
            
            return element.cloneNode(deep);
        } catch (error) {
            this.errorHandler?.handleError(error, 'cloneElement', { element, deep });
            return null;
        }
    }

    /**
     * Wait for element to appear in DOM
     * @param {string} selector - CSS selector
     * @param {number} timeout - Timeout in milliseconds
     * @param {Element} context - Context element (default: document)
     * @returns {Promise<Element|null>} Promise resolving to element or null
     */
    waitForElement(selector, timeout = 5000, context = document) {
        return new Promise((resolve) => {
            const startTime = Date.now();
            
            const checkElement = () => {
                try {
                    const element = this.querySelector(selector, context, false);
                    
                    if (element) {
                        resolve(element);
                        return;
                    }
                    
                    if (Date.now() - startTime >= timeout) {
                        resolve(null);
                        return;
                    }
                    
                    setTimeout(checkElement, 100);
                } catch (error) {
                    this.errorHandler?.handleError(error, 'waitForElement', { selector, timeout, context });
                    resolve(null);
                }
            };
            
            checkElement();
        });
    }

    /**
     * Escape HTML to prevent XSS
     * @param {string} text - Text to escape
     * @returns {string} Escaped text
     */
    escapeHTML(text) {
        try {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        } catch (error) {
            this.errorHandler?.handleError(error, 'escapeHTML', { text });
            return String(text).replace(/[&<>"']/g, (match) => {
                const escapeMap = {
                    '&': '&amp;',
                    '<': '&lt;',
                    '>': '&gt;',
                    '"': '&quot;',
                    "'": '&#39;'
                };
                return escapeMap[match];
            });
        }
    }

    /**
     * Get text content safely
     * @param {Element} element - Element to get text from
     * @param {boolean} normalize - Whether to normalize whitespace
     * @returns {string} Text content
     */
    getTextContent(element, normalize = true) {
        try {
            if (!element) {
                return '';
            }
            
            let text = element.textContent || '';
            
            if (normalize) {
                text = text.replace(/\s+/g, ' ').trim();
            }
            
            return text;
        } catch (error) {
            this.errorHandler?.handleError(error, 'getTextContent', { element, normalize });
            return '';
        }
    }

    /**
     * Clear cache
     */
    clearCache() {
        this.cache.clear();
    }

    /**
     * Get cache statistics
     * @returns {Object} Cache statistics
     */
    getCacheStats() {
        return {
            size: this.cache.size,
            timeout: this.cacheTimeout,
            isInitialized: this.isInitialized
        };
    }

    /**
     * Cleanup DOM utilities
     */
    cleanup() {
        console.log('[DOMUtils] Starting cleanup...');
        
        try {
            this.clearCache();
            this.isInitialized = false;
            
            console.log('[DOMUtils] Cleanup completed');
        } catch (error) {
            console.error('[DOMUtils] Error during cleanup:', error);
        }
    }
}

// Export for use in other modules
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DOMUtils;
} else {
    window.DOMUtils = DOMUtils;
}
